drop trigger if exists hop_prev_aiud on hop;
drop trigger if exists hop_prev_biu on hop;

alter table hop drop column if exists prev_hop_id cascade;

alter table hop add if not exists from_location_id bigint references location(id) deferrable;

-- As there are many hops, the following will take looooooong time to run for the farms.
-- We need to run this evolution in parallel with the running application.
-- Based on the size and history of the farm, it can take minutes

/* This HAS to be run after this evolution is place, hut we rather run it after the evolutions, so that
* the deployment does not take very long.
*

alter table hop disable trigger user;
with x as (select h.id, h.sow_id, h.boar_id, h.gilt_id, h.actor_date, h.to_location_id,
             coalesce(lag(h.to_location_id) over (partition by h.gilt_id, h.sow_id, h.boar_id order by h.actor_date, h.id), g.created_location_id, s.created_location_id, b.created_location_id) from_loc_id
           from hop h
           left join gilt g on g.id = h.gilt_id
           left join sow s on s.id = h.sow_id
           left join boar b on b.id = h.boar_id
           order by h.gilt_id, h.sow_id, h.boar_id,  h.actor_date, h.id)
update hop set from_location_id = x.from_loc_id from x where hop.id = x.id and hop.from_location_id is distinct from x.from_loc_id;
alter table hop enable trigger user;
*/

create trigger hop_prev_biu
  before insert or update of actor_date, id, sow_id, boar_id, gilt_id, to_location_id
  on hop
  for each row
execute procedure common.hop_prev_biutf();

drop trigger if exists hop_prev_aid on hop;
create trigger hop_prev_aid
  after insert or delete
  on hop
  for each row
execute procedure common.hop_prev_aiudtf();

drop trigger if exists hop_prev_au on hop;
create trigger hop_prev_au
    after update of id, actor_date, to_location_id, sow_id, boar_id, gilt_id
    on hop
    for each row when ((old.id, old.actor_date, old.to_location_id, old.sow_id, old.boar_id, old.gilt_id) is distinct from (new.id, new.actor_date, new.to_location_id, new.sow_id, new.boar_id, new.gilt_id))
execute procedure common.hop_prev_aiudtf();
