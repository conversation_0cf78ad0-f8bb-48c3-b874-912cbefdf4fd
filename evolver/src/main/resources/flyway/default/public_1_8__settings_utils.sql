create or replace function public.autoFinishFarrowingsOnFarm(org_id bigint) returns void as $$
declare
  auto_finishing text;
  hours_delay    numeric;
begin
  perform public.set_system_persona(org_id);
  auto_finishing := public.get_setting('auto_finishing');
  if (auto_finishing ~ '^\d+$') then
    hours_delay := to_number(auto_finishing, '0000000');
    if (hours_delay is null) then
      return;
    end if;
    if exists(select s.id from serving s inner join farrowevent f on s.id = f.serving_id
    where s.farrow_starttime is not null
          and s.farrow_starttime < now() - (hours_delay * interval '1 hour')
          and f.update_date < now() - interval '10 minutes'
          and s.farrow_endtime is null) then
      update farrowevent x
      set state = 2, comment = y.komment
      from (
             select distinct on (s.id)
               s.id serving_id, f.id, '(Automatic) ' || coalesce(f.comment, '') komment
             from serving s
               inner join farrowevent f on s.id = f.serving_id
             where s.farrow_starttime is not null
                   and s.farrow_starttime < now() - (hours_delay * interval '1 hour')
                   and f.update_date < now() - interval '10 minutes'
                   and s.farrow_endtime is null
             order by s.id, f.actor_date desc, f.create_date desc, f.id desc) y where x.id = y.id;
    end if;
  end if;
end;
$$
language plpgsql volatile set search_path to default;

create or replace function public.get_setting(key text) returns text as
$BODY$
declare
  thevalue text;
begin
  select distinct on (name) value
  into thevalue
  from (
         with recursive h (id, root_id, parent_id) as (
           select
             x.id, x.root_id, x.parent_id, x.prefix, x.name
           from holding x join organization o on o.holding_id = x.id
           where o.id = get_tenant_organization()
           union all
           select
             p.id, p.root_id, p.parent_id, p.prefix, p.name
           from holding p join h on p.id = h.parent_id
         )
         select
           name as name, entryvalue as value, priority as priority from settings where
           type = 'a' and id = get_tenant_account() and name = key
         union all
         select
           name as name, entryvalue as value, priority as priority from settings where
           type = 'o' and id = get_tenant_organization() and name = key
         union all
         select
           s.name as name, s.entryvalue as value, s.priority as priority from
           settings s join h on s.id = h.id and s.type = 'h' and s.name = key
         union all
         select
           name as name, entryvalue as value, priority as priority from settings where type = 's' and name = key
       ) x
  order by name, priority desc;

  return thevalue;
end;
$BODY$
language plpgsql stable strict;

create or replace function public.week_start(dt date, week_1st integer, week_starting_day integer)
  returns date as
$body$
declare
  dow         integer;
  tmp_date    date;
  jan_x       date;
  week_number integer;
  year_number integer;
  utc_weekday integer;
begin

  dow := cast(extract('dow' from dt) as integer);
  dow := dow - week_starting_day;

  if (dow < 0)
  then
    dow := dow + 7;
  end if;

  tmp_date := dt - dow;
  tmp_date := tmp_date + (6 - week_1st);

  week_number := (cast(extract('doy' from tmp_date) as integer) + 6) / 7;
  year_number := cast(extract('year' from tmp_date) as integer);

  jan_x := to_date(year_number || '-01-' || (1 + week_1st), 'YYYY-MM-DD');
  utc_weekday := cast(extract('dow' from jan_x) as integer);

  if (utc_weekday < week_starting_day)
  then
    utc_weekday := utc_weekday + 7;
  end if;

  return jan_x + ((7 * (week_number - 1)) + week_starting_day - utc_weekday);
end;
$body$
language plpgsql immutable strict;

create or replace function public.week_start(dt date)
  returns date as $$
select public.week_start(dt, get_setting('week_1st') :: integer, get_setting('week_starting_day') :: integer);
$$ language sql stable strict;

create or replace function public.extract_week(dt date, week_1st integer, week_starting_day integer)
  returns integer as
$EXTRACT_WEEK$
declare
  week_1st          integer;
  week_starting_day integer;
  day_of_week       integer;
  result            integer;
begin
  if (dt is null) then
    return null;
  end if;
  week_1st := get_setting('week_1st');
  week_starting_day := get_setting('week_starting_day');
  day_of_week := extract(dow from dt);
  day_of_week := day_of_week - week_starting_day; -- Adjust to the custom week (0 based)
  if (day_of_week < 0) then
    day_of_week = day_of_week + 7;
  end if;
  dt := dt - day_of_week + (6 - week_1st); -- d is now the 1st day of the week + day in 1st week
  result := floor((extract(doy from dt) + 6) / 7);
  return result;
end;
$EXTRACT_WEEK$ language plpgsql immutable strict;

create or replace function public.extract_week(dt date)
  returns integer as $$
select public.extract_week(dt, get_setting('week_1st') :: integer, get_setting('week_starting_day') :: integer);
$$ language sql stable strict;

create or replace function public.extract_weekinfo(dt date, out week integer, out year integer)
  returns record as
$EXTRACT_WEEKINFO$
declare
  week_1st          integer;
  week_starting_day integer;
  day_of_week       integer;
  result            integer;
begin
  if (dt is null) then
    return;
  end if;
  week_1st := get_setting('week_1st');
  week_starting_day := get_setting('week_starting_day');
  day_of_week := extract(dow from dt);
  day_of_week := day_of_week - week_starting_day; -- Adjust to the custom week (0 based)
  if (day_of_week < 0) then
    day_of_week = day_of_week + 7;
  end if;
  dt := dt - day_of_week + (6 - week_1st); -- d is now the 1st day of the week + day in 1st week
  week := floor((extract(doy from dt) + 6) / 7);
  year := extract('year' from dt);
  return;
end;
$EXTRACT_WEEKINFO$ language plpgsql stable strict;

do $$
begin
  if not exists(select 1
                from information_schema.columns
                where table_schema = 'public'
                      and table_name = 'translation_all'
                      and column_name = 'original') then

    alter table translation_all add original text;

    create or replace view translation as
      select
        language_id, messagekey, translation, original
      from translation_all
      where account_id = get_tenant_account();

    create or replace rule _delete as
    on delete to translation do instead delete from translation_all
    where translation_all.account_id = get_tenant_account() and translation_all.language_id = old.language_id and
          translation_all.messagekey = old.messagekey;

    create or replace rule _insert as
    on insert to translation do instead insert into translation_all (account_id, language_id, messagekey, translation, original)
    values (get_tenant_account(), new.language_id, new.messagekey, new.translation, new.original);

    create or replace rule _update as
    on update to translation do instead update translation_all
    set language_id = new.language_id, messagekey = new.messagekey, translation = new.translation,
      original      = new.original
    where translation_all.account_id = get_tenant_account() and translation_all.language_id = old.language_id and
          translation_all.messagekey = old.messagekey;
  end if;
end;
$$;

do $$
begin
  if not exists(select 1 from information_schema.columns where
    table_schema = 'public' and table_name = 'translation_all' and column_name = 'update_date') then
    alter table translation_all add update_date timestamp with time zone not null default now();
    create trigger translation_all_biur before insert or update on translation_all
    for each row execute procedure public.general_updated_noaccount_biurtf();
  end if;
end;
$$;


