package domainz.journal

import enumeratum.{ Enum, EnumEntry }

import scala.collection.immutable

sealed trait StocktakingJournalTypeEnum extends EnumEntry {
  def dbValue: Short
}

object StocktakingJournalTypeEnum extends Enum[StocktakingJournalTypeEnum] {
  val values: immutable.IndexedSeq[StocktakingJournalTypeEnum] = findValues

  def withDbValueOpt(dbValue: Short): Option[StocktakingJournalTypeEnum] = StocktakingJournalTypeEnum.values.find(_.dbValue == dbValue)
  def withDbValue(dbValue: Short): StocktakingJournalTypeEnum = withDbValueOpt(dbValue).get

  case object BeforeStocktaking extends StocktakingJournalTypeEnum {
    override val dbValue: Short = 0
  }
  case object AtStocktaking extends StocktakingJournalTypeEnum {
    override val dbValue: Short = 1
  }
  case object AfterStocktaking extends StocktakingJournalTypeEnum {
    override val dbValue: Short = 2
  }
}
