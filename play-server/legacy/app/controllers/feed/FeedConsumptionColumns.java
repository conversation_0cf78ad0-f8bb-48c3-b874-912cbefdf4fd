package controllers.feed;

import controllers.LocationUsageType;
import controllers.ScreenColumns;
import models.FeedConsumption;
import models.handlers.SettingsHandler;
import play.i18n.Messages;
import security.ApplicationUser;

import jakarta.persistence.EntityManager;

import static controllers.GridColumn.Align.right;
import static controllers.GridColumn.ColumnTemplate.*;
import static controllers.GridColumn.ColumnType.*;
import static controllers.GridColumn.Editor.*;
import static controllers.GridColumn.Filter.*;
import static controllers.GridColumn.Formatter.*;
import static controllers.GridColumn.Validator.*;

public class FeedConsumptionColumns extends ScreenColumns<FeedConsumption> {

    public FeedConsumptionColumns(final Messages messages, final ApplicationUser user, EntityManager em, SettingsHandler settingsHandler) {
		addCol(col("locationId").name("js.label.location").editor(locations("['ALL']", LocationUsageType.all, "measureDate", false))
                .validator(all(mandatory, locationTypeValidator("['ALL']", false), locationValid(LocationUsageType.all, "measureDate")))
                .formatter(formatLocation(em)).width(12).align(right).filter(filterLocation).filterValue("null"));
		addCol(col("inhabitanttype_code").type(select).name("js.label.inhabitant").formatter(formatTranslate("js.label.inhabitant", true)).filter(filterInhabitants("a", "s", "b", "g", "w", "f", "p", "pa")).sortable(true).width(10).cannotTriggerInsert(true).editor(null /*setting in runtime*/));
		addCol(col("feedName").type(text).editor(editorFromService("FeedConsumptionData", "gridEditor")).validator(mandatory).sortable(true));
		addCol(col("description").type(text).editor(maxLength(30)).sortable(true));
        addCol(col("type").name("js.label.feedconsumptiontype").sortable(true).editor(feedConsumptionTypeSelectEditor).formatter(formatTranslateNotEmpty("js.enum.feedConsType",false)).cannotTriggerInsert(true).filter(filterFeedConsumptionType));
		addCol(col("consumption").localizedName(messages.apply("js.label.consumptionin", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).editor(weightKg(2)).formatter(formatWeightKg(2)).filter(filterWeightKg).sortable(true).width(12).aggSum()
                .validator(maxValValidator(12, 2)));
		addCol(col("dryMatter").name("js.label.drymatter").type(number).editor(decimalPlaces(2)).validator(minMaxValValidator(0, 5, 2)).formatter(formatDecimalPlaces(2)).sortable(true).width(12));
		addCol(col("feedUnit").name("js.label.energy_per_one").type(number).editor(weightKg(2)).validator(minMaxValValidator(0, 6, 3)).formatter(formatWeightKg(2)).filter(filterWeightKg).sortable(true)
                .parameters("M('js.label.weight.unit.' + settings.getOrg('weight'))")
                .parameters("M('js.label.energy.unit.' + settings.getOrg('energy'))"));
		addCol(col("fromDate").type(date).sortable(true).optional(true));
		addCol(col("measureDate").name("js.label.consumption.date").type(date).sortable(true));
		addCol(col("price").name("js.label.price_per_one").type(number).editor(weightInvKg(3)).formatter(formatWeightInvKg(3)).filter(filterWeightInvKg).sortable(true).aggSum()
                .parameters("M('js.label.weight.unit.' + settings.getOrg('weight'))")
                .parameters("settings.getOrg('currency')").validator(minMaxValValidator(0, 12, 5)));
		addCol(col("rawProteinPercent").type(number).editor(decimalPlaces(2)).validator(minMaxValValidator(0, 12, 2)).formatter(formatDecimalPlaces(2)).sortable(true).aggAvg());
		addCol(col("gcalcium").type(number).editor(decimalPlaces(2)).validator(minMaxValValidator(0, 12, 5)).formatter(formatDecimalPlaces(2)).sortable(true).aggAvg());
		addCol(col("gkaliumKg").type(number).editor(decimalPlaces(2)).validator(minMaxValValidator(0, 12, 5)).formatter(formatDecimalPlaces(2)).sortable(true).aggAvg());
		addCol(col("gphosforKg").type(number).editor(decimalPlaces(2)).validator(minMaxValValidator(0, 12, 5)).formatter(formatDecimalPlaces(2)).sortable(true).aggAvg());
		addCol(col("gsodiumKg").type(number).editor(decimalPlaces(2)).validator(minMaxValValidator(0, 12, 5)).formatter(formatDecimalPlaces(2)).sortable(true).aggAvg());
        addCol(col("comment").type(text).editor(maxLength(150)).sortable(true));
        addCol(col("feedStation").type(text).editor(maxLength(150)).sortable(true));
        addCol(col("feedSystem").type(text).editor(maxLength(150)).sortable(true));
		//addCol(col("actorDate").type(date).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval).sortable(true));
        addCol(col("recipeName").type(text).editor(maxLength(50)).sortable(true).cannotTriggerInsert(true));
        addCol(col("supplierId").name("js.label.supplier").editor(businessAutoCompleteEditor).formatter(formatBusiness).width(12).filter(filterBusiness).sortable(true).filterValue("null").cannotTriggerInsert(true));
        addCol(col("actorId").width(10).name("js.label.actor").editor(colleagueEditor).formatter(formatColleague(em)).filter(filterColleague).sortable(true));
        addCol(col("createAccountId").template(em, createAccountId));
        addCol(col("createDate").template(em, createDate));
        addCol(col("createUi").template(em, createUi));
        addCol(col("updateAccountId").template(em, updateAccountId));
        addCol(col("updateDate").template(em, updateDate));
	}
}
