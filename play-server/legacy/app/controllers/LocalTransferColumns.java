package controllers;

import models.PigqualityLoc;
import models.TransferLoc;
import models.handlers.SettingsHandler;
import play.i18n.Messages;
import security.ApplicationUser;

import jakarta.persistence.EntityManager;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static controllers.GridColumn.Align.center;
import static controllers.GridColumn.Align.left;
import static controllers.GridColumn.Align.right;
import static controllers.GridColumn.ColumnType.*;
import static controllers.GridColumn.Editor.*;
import static controllers.GridColumn.Filter.*;
import static controllers.GridColumn.Formatter.*;
import static controllers.GridColumn.Validator.aboveZero;
import static controllers.GridColumn.Validator.mandatory;
import static controllers.GridColumn.Validator.minMaxValValidator;

public class LocalTransferColumns extends ScreenColumns<TransferLoc> {
    public final Map<String, GridColumn<PigqualityLoc>> colDefsExcelDetail = new HashMap<>();
    public final List<String> colOrderExcelDetail = new ArrayList<>();

    public LocalTransferColumns(final Messages messages, final ApplicationUser user, EntityManager em, SettingsHandler settingsHandler) {

        addCol(col("inhabitantTypeCode").name("js.label.inhabitanttype").filter(filterInhabitantTypesOnlyGroup).sortable(false).width(15)); // editor is set in runtime
        addCol(col("actorDate").type(dateTime).name("js.label.date").sortable(true).validator(mandatory).cannotTriggerInsert(true));
        addCol(col("transferName").type(text).editor(maxLength(30)).width(12).sortable(true).filter(filterText).cannotTriggerInsert(true));
        addCol(col("actorId").width(10).name("js.label.actor").editor(colleagueEditor).formatter(formatColleague(em)).filter(filterColleague).cannotTriggerInsert(true).sortable(true));
        addCol(col("pigAmount").name("js.label.pigamountsum").type(number).width(12).editor(null).sortable(true).cannotTriggerInsert(true).filter(filterNumber).aggSum());
        if (settingsHandler.useAgeInGroups(user)) {
            addCol(col("averageAge").type(number).editor(null).formatter(formatDecimalPlaces(0)).width(10).optional(true).cannotTriggerInsert(true));
        }
        addCol(col("pigLiveWeight").localizedName(messages.apply("js.label.pigliveweightsum.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
            .type(number).width(17).editor(null).formatter(formatWeightKg(3)).filter(filterWeightKg).sortable(true).cannotTriggerInsert(true).aggSum());
        addCol(col("price").name("js.label.pricesum").type(number).width(16).editor(null).sortable(true).cannotTriggerInsert(true).filter(filterNumber).aggSum());
        addCol(col("createAccountId").formatter(formatColleague(em)).width(10).filter(filterColleague).sortable(true).editor(null));
        addCol(col("createDate").type(timestamp).sortable(true));
        addCol(col("createUi").toolTip("js.label.createui.tooltip").formatter(formatUi).filter(filterUi).align(center).width(2).sortable(true));
        addCol(col("updateAccountId").formatter(formatColleague(em)).width(10).filter(filterColleague).sortable(true));
        addCol(col("updateDate").type(timestamp).sortable(true));
        addCol(col("comment").type(text).editor(maxLength(250)).width(60).sortable(true).cannotTriggerInsert(true));
        addCol(col("lorryCountryId").name("js.label.lorrycountry").type(text).editor(countryEditor("id", true)).formatter(formatCountry).width(10).sortable(true).cannotTriggerInsert(true).optional(true));
        addCol(col("lorryPlateNumber").type(text).editor(maxLength(16)).width(15).sortable(true).cannotTriggerInsert(true).optional(true));
        addCol(col("trailerCountryId").name("js.label.trailercountry").type(text).editor(countryEditor("id", true)).formatter(formatCountry).width(10).sortable(true).cannotTriggerInsert(true).optional(true));
        addCol(col("trailerPlateNumber").type(text).editor(maxLength(16)).width(15).sortable(true).cannotTriggerInsert(true).optional(true));
        // this is just "copy-paste" of LocalTransferPigQualityColumns, plus added '_y' to the begining (to avoid colisions)
        addColExcelDetail(colPQ("fromLocationId").name("js.label.location.from").editor(null).formatter(formatLocation(em)).width(12).align(right).filter(filterLocation).filterValue("null").validator(mandatory));
        addColExcelDetail(colPQ("fromInhabitanttype_code").editor(null).width(15).name("js.label.inhabitanttype.from").cannotTriggerInsert(true));
        addColExcelDetail(colPQ("toLocationId").name("js.label.location.to").editor(null).formatter(formatLocation(em)).width(12).align(right).filter(filterLocation).filterValue("null").validator(mandatory));
        addColExcelDetail(colPQ("toInhabitanttype_code").editor(null).width(15).name("js.label.inhabitanttype.to").cannotTriggerInsert(true));
        addColExcelDetail(colPQ("pigQualityTypeCode").editor(codesAutoComplete("PigqualityTypes")).width(9).formatter(formatCodeTypes(em, "PigqualityTypes")).cannotTriggerInsert(true));
        addColExcelDetail(colPQ("pigAmount").type(number).editor(decimalPlaces(0)).formatter(formatDecimalPlaces(0)).width(10).cannotTriggerInsert(true).validator(aboveZero));
        if (settingsHandler.useAgeInGroups(user)) {
            addColExcelDetail(colPQ("age").type(number).editor(decimalPlaces(0)).formatter(formatDecimalPlaces(0)).width(10).cannotTriggerInsert(true).validator(aboveZero));
        }
        addColExcelDetail(colPQ("pigLiveWeight").localizedName(messages.apply("js.label.pigliveweightsum.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
            .type(number).editor(weightKg(3)).formatter(formatWeightKg(3)).filter(filterWeightKg).width(13).cannotTriggerInsert(true).validator(aboveZero));
        addColExcelDetail(colPQ("price").name("js.label.transferprice").type(number).editor(decimalPlaces(3)).formatter(formatDecimalPlaces(3)).width(15).cannotTriggerInsert(true).aggSum());
        addColExcelDetail(colPQ("leanPercent").type(number).editor(decimalPlaces(2, 5)).formatter(formatDecimalPlaces(2)).width(7).cannotTriggerInsert(true).validator(minMaxValValidator(0, 100)));
        addColExcelDetail(colPQ("comment").type(text).editor(maxLength(250)).width(40).align(left).cannotTriggerInsert(true));
    }

    private GridColumn<PigqualityLoc> colPQ(String id) {
        return new GridColumn<PigqualityLoc>().id(id);
    }

    public static final String EXCEL_DETAIL_PREFIX = "y_";

    private void addColExcelDetail(GridColumn<PigqualityLoc> col) {
        col.field(EXCEL_DETAIL_PREFIX + col.getField());
        colDefsExcelDetail.put(EXCEL_DETAIL_PREFIX + col.getId(), col);
        colOrderExcelDetail.add(EXCEL_DETAIL_PREFIX + col.getId());
    }
}
