package controllers;

import models.SparePartStorage;

import jakarta.persistence.EntityManager;

import static controllers.GridColumn.Align.right;
import static controllers.GridColumn.ColumnType.*;
import static controllers.GridColumn.Editor.*;
import static controllers.GridColumn.Filter.*;
import static controllers.GridColumn.Formatter.*;

public class SparePartStorageColumns extends ScreenColumns<SparePartStorage> {
    public SparePartStorageColumns(EntityManager em) {
        addCol(col("name").type(text).editor(maxLength(50)).width(20).sortable(true));
        addCol(col("locationId").name("js.label.location").editor(locations("['ALL']", LocationUsageType.all)).formatter(formatLocation(em)).width(12).align(right).filter(filterLocation).filterValue("null").sortable(true));
        addCol(col("validFrom").name("js.label.valid.from").type(date));
        addCol(col("validTo").name("js.label.valid.to").type(date));
        addCol(col("createAccountId").template(em, GridColumn.ColumnTemplate.createAccountId));
        addCol(col("createDate").template(em, GridColumn.ColumnTemplate.createDate));
        addCol(col("createUi").template(em, GridColumn.ColumnTemplate.createUi));
        addCol(col("updateAccountId").template(em, GridColumn.ColumnTemplate.updateAccountId));
        addCol(col("updateDate").template(em, GridColumn.ColumnTemplate.updateDate));
    }
}
