package controllers;

import legacy.RoutesPrefix;

/*
 ========================================================================================================================
 Generating of reverse routes is disabled in play-server project via `RoutesKeys.generateReverseRouter := false`
 due to strange continuous recompilation of play-server even nothing has changed. Disabling generating reverse routes
 seems to fix the issue.

 These reverse routes and those in [[controllers.ext.routes]] should be only reverse routes used across the project.
 When reverse routes are generated, they follow slightly different package structures, e.g.:
    `new controllers.ReverseAssets` instead of `new RoutesScala.ReverseAssets`
    `new controllers.javascript.ReverseAssets` instead of `new RoutesScala.JavascriptReverseAssets`
 etc.

 So if we need to enable generating reverse routes for some reason in the future again, these hardcoded won't clash with
 generated ones. Copied ones can be compared (formatting is turned off in copied classes) and/or adjusted.
 Just both routes.java files need to be commented out before enabling generating reverse routes again.
 ========================================================================================================================
*/

public class routes {

    // This instantiates generated reverse route
    // public static final controllers.ReverseAssets Assets = new controllers.ReverseAssets(RoutesPrefix.byNamePrefix());
    // This instantiates copied reverse route from generated one
    public static final RoutesScala.ReverseAssets Assets = new RoutesScala.ReverseAssets(RoutesPrefix.byNamePrefix());
    // public static final controllers.ReverseApplicationController ApplicationController = new controllers.ReverseApplicationController(RoutesPrefix.byNamePrefix());
    public static final RoutesScala.ReverseApplicationController ApplicationController = new RoutesScala.ReverseApplicationController(RoutesPrefix.byNamePrefix());
    // public static final controllers.ReverseIssueController IssueController = new controllers.ReverseIssueController(RoutesPrefix.byNamePrefix());
    public static final RoutesScala.ReverseIssueController IssueController = new RoutesScala.ReverseIssueController(RoutesPrefix.byNamePrefix());

    public static class javascript {

        // public static final controllers.javascript.ReverseAssets Assets = new controllers.javascript.ReverseAssets(RoutesPrefix.byNamePrefix());
        public static final RoutesScala.JavascriptReverseAssets Assets = new RoutesScala.JavascriptReverseAssets(RoutesPrefix.byNamePrefix());
        // public static final controllers.javascript.ReverseApplicationController ApplicationController = new controllers.javascript.ReverseApplicationController(RoutesPrefix.byNamePrefix());
        public static final RoutesScala.JavascriptReverseApplicationController ApplicationController = new RoutesScala.JavascriptReverseApplicationController(RoutesPrefix.byNamePrefix());
        // public static final controllers.javascript.ReverseIssueController IssueController = new controllers.javascript.ReverseIssueController(RoutesPrefix.byNamePrefix());
        public static final RoutesScala.JavascriptReverseIssueController IssueController = new RoutesScala.JavascriptReverseIssueController(RoutesPrefix.byNamePrefix());

    }

}
