package domains.medicine

import reactapp.shared.medicine.{ CureGrid, PrescriptionGrid }
import scalatestfw._

import java.time.Instant

class PrescriptionPageWebTest extends WebBrowserSpec {
  override def databaseVersion: Int = 8
  val prescriptionGridSelector = ".optional-details"
  val pageLink = "prescriptionNew"

  val (rowId1, rowId2, rowId3, rowId4) = (418205, 418208, 418209, 418210)

  "Prescripiton page" must {

    val prescription = Seq(
      PrescriptionGrid.Name -> "Test1",
      PrescriptionGrid.Medicine -> "Paralen",
      PrescriptionGrid.ActorDate -> Instant.now(),
      PrescriptionGrid.PackagesCount -> 100,
      PrescriptionGrid.PackagePrice -> 1.23,
      PrescriptionGrid.MedicineApplicationCode -> "(3) oral",
    )

    val checkPrescription = Seq(
      PrescriptionGrid.Name -> "Test1",
      PrescriptionGrid.Medicine -> "Paralen",
      PrescriptionGrid.ActorDate -> Instant.now(),
      PrescriptionGrid.PackagesCount -> 100,
      PrescriptionGrid.PackagePrice -> 1.23,
      PrescriptionGrid.MedicineApplicationCode -> "(3) oral",
      PrescriptionGrid.PackageSize -> 20,
      PrescriptionGrid.Unit -> "tablet",
    )

    "create new prescription" in {
      login(pageLink, farmId = Farm1.id)

      clickViewsBtn()
      toggleAllGridColumns()
      closeViews()

      val row = grid(prescriptionGridSelector, PrescriptionGrid)
        .waitForSomeRows()
        .addNewRow()
        .enterValues(prescription: _*)

      row.saveAndReload()
      row.validateValues(checkPrescription)
    }

    "update existing prescription" in {
      login(pageLink, farmId = Farm1.id)

      clickViewsBtn()
      toggleAllGridColumns()
      closeViews()

      val prescriptionGrid = grid(prescriptionGridSelector, PrescriptionGrid)
        .waitForSomeRows()

      val row = prescriptionGrid.rowByPermanentId(rowId2)
      row.enterValuesSaveReloadAndValidate(prescription, checkPrescription.toMap) // input not found exception
    }

    "delete prescription" in { // the delete tests can sometimes fail on unexpected alert open exception
      login(pageLink, farmId = Farm1.id)
      val prescriptionGrid = grid(prescriptionGridSelector, PrescriptionGrid, virtualPaging = true)
        .waitForSomeRows()
      val row = prescriptionGrid.rowByPermanentId(rowId2)
      row.clickMe()
      clickRemoveBtn(idOpt = Some("PrescriptionGridDeleteBtn"))
      clickFoundationModalYesBtn()
      assert(!prescriptionGrid.getVisiblePermanentRows.exists(_.permanentId.equals(s"$rowId2")))
      reloadPageAndWaitForApp()
      assert(!prescriptionGrid.getVisiblePermanentRows.exists(_.permanentId.equals(s"$rowId2")))
    }

    "prescription warning against delete" in { // the delete tests can sometimes fail on unexpected alert open exception
      login(pageLink, farmId = Farm1.id)
      val prescriptionGrid = grid(prescriptionGridSelector, PrescriptionGrid, virtualPaging = true)
        .waitForSomeRows()
      val row = prescriptionGrid.rowByPermanentId(rowId1)
      row.clickMe()
      clickRemoveBtn(idOpt = Some("PrescriptionGridDeleteBtn"))
      clickFoundationModalYesBtn()
      assertDeleteErrorMessage("There are 2 cures, 1 medicine usages and 0 medicine transfers bonded to this prescription. Please remove them first.")
      reloadPageAndWaitForApp()
      assert(prescriptionGrid.getVisiblePermanentRows.count(_.permanentId.equals(s"$rowId1")) >= 0)
    }

    "check sortings" in {
      login(pageLink, farmId = Farm1.id)
      val prescriptionGrid: GridElement[PrescriptionGrid] = grid(prescriptionGridSelector, PrescriptionGrid, virtualPaging = true)
        .waitForSomeRows()
      prescriptionGrid
        .enterFilterValues(PrescriptionGrid.ActorDate -> "1/1/14..9/1/14")
        .waitForVisiblePermanentRowsCountToBe(2)
        .checkSortings(
          PrescriptionGrid.values.toSet.filterNot(col => {
            col == PrescriptionGrid.Name ||
            col == PrescriptionGrid.Medicine ||
            col == PrescriptionGrid.ActorDate ||
            col == PrescriptionGrid.PackageSize ||
            col == PrescriptionGrid.Unit ||
            col == PrescriptionGrid.PackagesCount ||
            col == PrescriptionGrid.MedicineApplicationCode ||
            col == PrescriptionGrid.ActorId
          }),
        )
    }

  }

  private val newCure = Seq(
    CureGrid.Illness -> "(52) 2RB",
    CureGrid.Inhabitant -> "Sow",
    CureGrid.Dosage -> 42,
    CureGrid.Target -> 1,
    CureGrid.TargetUnit -> "Animal",
  )

  private val newCureValidation = Seq(
    CureGrid.Illness -> "(52) 2RB",
    CureGrid.Inhabitant -> "Sow",
    CureGrid.Dosage -> 42,
    CureGrid.Target -> 1,
    CureGrid.TargetUnit -> "Animal",
    CureGrid.Unit -> "kg",
  )

  "Cure detail" must {
    def prescriptionGrid = grid(prescriptionGridSelector, PrescriptionGrid)

    "create new cure" in {
      login(pageLink, farmId = Farm1.id)
      prescriptionGrid.waitForSomeRows()
      val prescriptionRow = prescriptionGrid.rowByPermanentId(rowId1)
      prescriptionRow.clickMe()
      toggleDetails
      val cureGrid = grid("#CureGrid", CureGrid)
        .waitForSomeRows()
      val row = cureGrid
        .targetNewRow()
        .rememberByPosition()
      row.enterValuesSaveAndValidate(
        values = newCure,
        saveSuccessfullyOverrideFn = () => {
          row.dealWithWarnings()
          prescriptionRow.dealWithWarnings()
          clickSaveBtn()
          row.validateSuccessfulSave()
        },
      )
      reloadPageAndWaitForApp()
      prescriptionGrid.waitForSomeRows()
      prescriptionRow.clickMe()
      toggleDetails
      val cureGrid2 = grid("#CureGrid", CureGrid)
        .waitForSomeRows()
      cureGrid2.getVisiblePermanentRows.filter(_.cell(CureGrid.Illness).textValue.equals("(52) 2RB")).head.validateValues(newCureValidation)
    }

    "update existing cure" in {
      login(pageLink, farmId = Farm1.id)
      prescriptionGrid.waitForSomeRows()
      val prescriptionRow = prescriptionGrid.rowByPermanentId(rowId1)
      prescriptionRow.clickMe()
      toggleDetails
      val cureGrid = grid("#CureGrid", CureGrid)
        .waitForSomeRows()
      val row = cureGrid.rowByPermanentId(rowId3)
      row.enterValues(newCure: _*)
      prescriptionRow.dealWithWarnings()
      row.saveSuccessfully()
      reloadPageAndWaitForApp()
      prescriptionGrid.waitForSomeRows()
      prescriptionRow.clickMe()
      toggleDetails

      cureGrid.rowByPermanentId(rowId3).validateValues(newCureValidation)
    }

    "delete existing cure" in { // the delete tests can sometimes fail on unexpected alert open exception
      login(pageLink, farmId = Farm1.id)
      prescriptionGrid.waitForSomeRows()
      val prescriptionRow = prescriptionGrid.rowByPermanentId(rowId1)
      prescriptionRow.clickMe()
      toggleDetails
      val cureGrid = grid("#CureGrid", CureGrid)
        .waitForSomeRows()
      val row = cureGrid.rowByPermanentId(rowId4)
      row.clickMe()
      clickRemoveBtn(idOpt = Some("CureGridDeleteBtn"))
      clickFoundationModalYesBtn()
      assert(!cureGrid.getVisiblePermanentRows.exists(_.permanentId.equals(s"$rowId4")))
      reloadPageAndWaitForApp()
      prescriptionGrid.waitForSomeRows()
      prescriptionRow.clickMe()
      toggleDetails
      assert(!cureGrid.getVisiblePermanentRows.exists(_.permanentId.equals(s"$rowId4")))
    }

    "cure warning against delete" in { // the delete tests can sometimes fail on unexpected alert open exception
      login(pageLink, farmId = Farm1.id)
      prescriptionGrid.waitForSomeRows()
      val prescriptionRow = prescriptionGrid.rowByPermanentId(rowId1)
      prescriptionRow.clickMe()
      toggleDetails
      val cureGrid = grid("#CureGrid", CureGrid)
        .waitForSomeRows()
      val row = cureGrid.rowByPermanentId(rowId3)
      row.clickMe()
      clickRemoveBtn(idOpt = Some("CureGridDeleteBtn"))
      clickFoundationModalYesBtn()
      assertDeleteErrorMessage("Cure can not be deleted, because there is 1 medicine usages with the cure.")
      reloadPageAndWaitForApp()
      prescriptionGrid.waitForSomeRows()
      prescriptionRow.clickMe()
      toggleDetails
      assert(cureGrid.getVisiblePermanentRows.count(_.permanentId.equals(s"$rowId3")) >= 0)
    }

    "check sortings" in {
      login(pageLink, farmId = Farm1.id)

      prescriptionGrid
        .waitForSomeRows()
        .rowByPermanentId(rowId1)
        .clickMe()

      toggleDetails

      grid("#CureGrid", CureGrid)
        .waitForSomeRows()
        .enterFilterValues(CureGrid.Illness -> "(57) Skinny")
        .waitForVisiblePermanentRowsCountToBe(2)
        .checkSortings()
    }

  }
}
