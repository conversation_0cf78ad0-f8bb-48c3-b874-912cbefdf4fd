package domains.observation

import domains.codetypes.CodeTypesService
import org.scalatest.matchers.should.Matchers
import reactapp.shared.CfSharedConfiguration
import reactapp.shared.codetypes.CodeTypesApi.CodeTypesAD
import reactapp.shared.codetypes.{ CodeTypesGrid, HeatNoServiceReasonGrid }
import reactapp.shared.observation.SowObservationApi.SaveMetadata
import reactapp.shared.observation.SowObservationGrid
import scalatestfw._
import sjsgrid.shared.common.ModelType
import sjsgrid.shared.common.config.SharedConfiguration
import sjsgrid.shared.form.UserInputsDTO
import sjsgrid.shared.grid.dto.{ GridSorting, RowSaveDTO }
import tenant.AutowireContext

import java.time.Instant
import java.util.UUID

class SowObservationApiTest extends ScalaSpec with ApiSpec with Matchers {

  import TestDbData._

  SharedConfiguration.set(CfSharedConfiguration)

  def databaseVersion: Int = 24

  implicit private val dbCreator: TestDbCreator = new TestDbCreator(DiffZip(databaseVersion))
  private val dbName = "SowObservationApiTest"

  private val animalId = "300304312"

  "Sow Observation Page" should "fail to save if no observation data are entered" in AutowireSpecContext.write(dbName) { ctx =>
    import ctx._
    val dataGetterParams = requestParams[SowObservationGrid]()
    val existingRows = getPageService(sowObservationService).list(dataGetterParams)

    val actorDate = Instant.now()
    val rowInputs = rowInputsNoObservationData(animalId, actorDate).toMap
    val metadata = SaveMetadata(idOpt = None, animalIdOpt = Some(animalId), sowNumberOpt = None, when = actorDate)

    // try to save new row
    val rowToSave = createNewRow(rowInputs, metadata)
    val saveResult = getPageService(sowObservationService).upsert(Seq(rowToSave))
    saveResult.head._2.isBad shouldEqual true

    val newRows = getPageService(sowObservationService).list(dataGetterParams)
    existingRows.length shouldEqual newRows.length
  }

  it should "create a reason via code type API and use it for Sow observation record" in AutowireSpecContext.write(dbName) { ctx =>
    import ctx._
    val dataGetterParams = requestParams[SowObservationGrid]()
    val existingRows = getPageService(sowObservationService).list(dataGetterParams)

    // save new HNS reason
    val hnsCode = generateHeatNoServiceReasonCode
    val hnsRowToSave = createHeatNoServiceReasonRow(hnsCode)
    val gridDefinition = HeatNoServiceReasonGrid
    val hnsSaveResult = getPageService(codeTypesService).upsert(Seq(hnsRowToSave), gridDefinition)
    hnsSaveResult.head._2.get should not be empty

    // save new Sow observation using the HNS reason
    val actorDate = Instant.now()
    val rowInputs = rowInputsWithObservationData(animalId, actorDate, hnsCode).toMap
    val metadata = SaveMetadata(idOpt = None, animalIdOpt = Some(animalId), sowNumberOpt = None, when = actorDate)
    val rowToSave = createNewRow(rowInputs, metadata)
    val saveResult = getPageService(sowObservationService).upsert(Seq(rowToSave))
    saveResult.head._2.isGood shouldEqual true

    val newRows = getPageService(sowObservationService).list(dataGetterParams)
    (existingRows.length + 1) shouldEqual newRows.length
  }

  it should "save everything in backfat table" in AutowireSpecContext.write(dbName) { ctx =>
    import ctx._
    val dataGetterParams = requestParams[SowObservationGrid]()
    val existingRows = getPageService(sowObservationService).list(dataGetterParams)

    // save new HNS reason
    val hnsCode = generateHeatNoServiceReasonCode
    val hnsRowToSave = createHeatNoServiceReasonRow(hnsCode)
    val gridDefinition = HeatNoServiceReasonGrid
    val hnsSaveResult = getPageService(codeTypesService).upsert(Seq(hnsRowToSave), gridDefinition)
    hnsSaveResult.head._2.get should not be empty

    // save new Sow observation with all backfat data
    val actorDate = Instant.now()
    val rowInputs = rowInputsAllBackfatData(animalId, actorDate, hnsCode).toMap
    val metadata = SaveMetadata(idOpt = None, animalIdOpt = Some(animalId), sowNumberOpt = None, when = actorDate)
    val rowToSave = createNewRow(rowInputs, metadata)
    val saveResult = getPageService(sowObservationService).upsert(Seq(rowToSave))
    saveResult.head._2.isGood shouldEqual true

    val newRows = getPageService(sowObservationService).list(dataGetterParams)
    (existingRows.length + 1) shouldEqual newRows.length
  }

  it should "sort columns" in AutowireSpecContext.write(dbName) { ctx =>
    import ctx._
    getGridSortableColumns(SowObservationGrid)
      .foreach { gridCol =>
        val requestParamsWithAscendingSorting = requestParams(Seq(GridSorting(gridCol, ascending = true)))
        getPageService(sowObservationService).list(requestParamsWithAscendingSorting).length should be > 0

        val requestParamsWithDescendingSorting = requestParams(Seq(GridSorting(gridCol)))
        getPageService(sowObservationService).list(requestParamsWithDescendingSorting).length should be > 0
      }
  }

  private def getPageService(pageService: SowObservationService)(implicit autowireContext: AutowireContext) =
    new pageService.ApiImpl(autowireContext)

  private def createNewRow(
    rowInputs: Map[SowObservationGrid with ModelType[Any], Any],
    metadata: SaveMetadata,
  ): RowSaveDTO[SowObservationGrid, SaveMetadata] = {
    new RowSaveDTO[SowObservationGrid, SaveMetadata](
      0,
      metadata,
      new UserInputsDTO[SowObservationGrid](rowInputs),
      Set.empty,
    )
  }

  private def rowInputsNoObservationData(animalId: String, actorDate: Instant): Seq[(SowObservationGrid with ModelType[Any], Any)] = {
    Seq(
      SowObservationGrid.AnimalId -> animalId,
      SowObservationGrid.ActorDate -> actorDate,
      SowObservationGrid.HopTo -> sowsLocation.id, // "Sows",
      SowObservationGrid.HeatNoService -> false,
    )
  }

  private def rowInputsWithObservationData(
    animalId: String,
    actorDate: Instant,
    hnsCode: String,
  ): Seq[(SowObservationGrid with ModelType[Any], Any)] = {
    Seq(
      SowObservationGrid.AnimalId -> animalId,
      SowObservationGrid.ActorDate -> actorDate,
      SowObservationGrid.HopTo -> sowsLocation.id, // "Sows",
      SowObservationGrid.HeatNoService -> true,
      SowObservationGrid.HeatNoServiceReason -> hnsCode,
    )
  }

  private def rowInputsAllBackfatData(
    animalId: String,
    actorDate: Instant,
    hnsCode: String,
  ): Seq[(SowObservationGrid with ModelType[Any], Any)] = {
    Seq(
      SowObservationGrid.AnimalId -> animalId,
      SowObservationGrid.ActorDate -> actorDate,
      SowObservationGrid.HopTo -> sowsLocation.id, // "Sows",
      SowObservationGrid.HeatNoService -> true,
      SowObservationGrid.HeatNoServiceReason -> hnsCode,
      SowObservationGrid.Layer1 -> 1,
      SowObservationGrid.Layer2 -> 2,
      SowObservationGrid.Layer3 -> 3,
      SowObservationGrid.Layer4 -> 4,
      SowObservationGrid.BodyCondition -> BigDecimal(21),
      SowObservationGrid.BodyConditionScoreCode -> "200", // "2 - Ideal"
      SowObservationGrid.FeedCurve -> 10,
      SowObservationGrid.BodyLength -> 11,
      SowObservationGrid.ActorId -> 16L, // "D48",
      SowObservationGrid.Comment -> "Comment",
    )
  }

  private def getPageService(codeTypesService: CodeTypesService)(implicit autowireContext: AutowireContext) =
    new codeTypesService.CodeTypesApiImpl()(autowireContext)

  private def generateHeatNoServiceReasonCode: String = UUID.randomUUID().toString.take(15) // Code is defined as 15 characters field

  private def createHeatNoServiceReasonRow(code: String): RowSaveDTO[CodeTypesGrid, CodeTypesAD] = {
    val newRowValues = Seq(
      HeatNoServiceReasonGrid.Code -> code,
      HeatNoServiceReasonGrid.Description -> "AaBbCc",
    )
    new RowSaveDTO[CodeTypesGrid, CodeTypesAD](
      0,
      CodeTypesAD(),
      new UserInputsDTO[CodeTypesGrid](newRowValues.toMap),
      Set.empty,
    )
  }

}
