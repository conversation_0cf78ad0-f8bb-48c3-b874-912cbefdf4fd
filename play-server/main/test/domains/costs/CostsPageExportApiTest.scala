package domains.costs

import org.scalatest.matchers.should.Matchers
import scalatestfw.{ AkkaSupport, ApiSpec, NewAppForTest, ScalaSpec }

class CostsPageExportApiTest extends ScalaSpec with ApiSpec with NewAppForTest with Matchers with AkkaSupport {

  override def databaseVersion: Int = 24

  it should "export grid" in {
    val columnsState = """{"fas":{"s":[{"c":"CostDate"}],"f":{"CostDate":[{"exactly":{"relative":1}}]},"pos":[]}}"""
    val exportPdfUrl = s"/export/costs?export=${ExportFormat.Pdf}"
    val costPageExport = app.injector.instanceOf[CostPageExport]
    val exportAction = costPageExport.exportCosts(columnsState)

    val result = callExport(exportPdfUrl, exportAction)

    val text = readPdfContent(result)
    text should not be empty
  }

}
