package reports.kpi.calculators.data.individual

import domainz.common.AnimalType
import org.scalatest.{ DoNotDiscover, Sequential }
import reports.kpi.calculators.data.{ KpiComparatorBase, KpiDataSourceFactory, KpiDataSourceParams }
import scalatestfw.KpiSpec

class LongTermSoldIndividualAnimalsKpisDataSourceScTest extends Sequential(
      new LongTermSoldIndividualAnimalsKpisDataSourceScTest1,
      new LongTermSoldIndividualAnimalsKpisDataSourceScTest2,
    )

object LongTermSoldIndividualAnimalsKpisDataSourceScTest {
  case object F extends KpiDataSourceFactory {
    def createAndRegister(params: KpiDataSourceParams): Unit =
      LongTermSoldIndividualAnimalsKpisDataSourceSc(params).registerKpiDataSources(params.registry)
  }
}

import reports.kpi.calculators.data.individual.LongTermSoldIndividualAnimalsKpisDataSourceScTest.F

@DoNotDiscover
class LongTermSoldIndividualAnimalsKpisDataSourceScTest1 extends KpiSpec with KpiComparatorBase {
  override def animalTypeOpts: Set[Option[AnimalType]] = Set(Some(AnimalType.Finisher), Some(AnimalType.Weaner))

  override def paramsModifier(p: KpiDataSourceParams): KpiDataSourceParams =
    p.copy(progenyTypeCodeOpt = Some("NF"))

  "LongTermSoldIndividualAnimalsKpisDataSourceScTest1" when {
    "compare new datasource with old datasource" should {
      compare(
        F,
        user => {
          p =>
            val (organizationsWithTimeZone, locations, periods, breeds, useServiceGroups, userAndFarm, em, animalType) = builder(p)

            new LongTermSoldIndividualAnimalsKpisDataSource(
              locations,
              periods,
              p.animalTypeOpt.get.dbCode,
              p.progenyTypeCodeOpt.get,
              p.settingsHandlerOpt.get,
              user,
              em,
            )
        },
      )
    }
  }
}

@DoNotDiscover
class LongTermSoldIndividualAnimalsKpisDataSourceScTest2 extends KpiSpec with KpiComparatorBase {
  override def animalTypeOpts: Set[Option[AnimalType]] = Set(None)

  "LongTermSoldIndividualAnimalsKpisDataSourceScTest2" when {
    "compare new datasource with old datasource" should {
      compare(
        F,
        user => {
          p =>
            val (organizationsWithTimeZone, locations, periods, breeds, useServiceGroups, userAndFarm, em, animalType) = builder(p)
            new LongTermSoldIndividualAnimalsKpisDataSource(
              locations,
              periods,
              p.settingsHandlerOpt.get,
              user,
              em,
            )
        },
      )
    }
  }
}
