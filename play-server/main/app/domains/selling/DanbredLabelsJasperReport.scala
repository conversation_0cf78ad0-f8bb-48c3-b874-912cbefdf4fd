package domains.selling

import javax.inject.{ Inject, Singleton }
import controllers.pdf.PdfUtils
import domains.common.ScalikeOps
import domains.selling.DanbredLabelsJasperReport._
import net.sf.jasperreports.engine.{ JREmptyDataSource, JRParameter, JasperCompileManager, JasperExportManager, JasperFillManager }
import play.api.Environment
import play.api.i18n.MessagesApi
import play.api.mvc.{ AbstractController, Action, AnyContent, ControllerComponents }
import play.i18n.Lang
import reactapp.shared.auth.Access
import scalikejdbc.{ DBSession, WrappedResultSet, scalikejdbcSQLInterpolationImplicitDef }
import sjsgrid.shared.common.PushkaPicklers.keepPushkaPicklersImport
import slogging.StrictLogging
import tenant.{ SessionProvider, WebActionBuilder, WebActionRequest }
import utils.jr.{ CloudfarmsFormatFactory, MapJRDataSource }

import java.time.LocalDate
import java.util.TimeZone
import scala.jdk.CollectionConverters._
import scala.concurrent.ExecutionContext

@Singleton
class DanbredLabelsJasperReport @Inject() (
  tenantActionBuilder: WebActionBuilder,
  messagesApi: MessagesApi,
  environment: Environment,
  cc: ControllerComponents,
  scalikeOps: ScalikeOps,
)(implicit env: Environment, ec: ExecutionContext, sessionProvider: SessionProvider) extends AbstractController(cc) with StrictLogging {
  keepPushkaPicklersImport
  import scalikeOps._

  def pdf(transferId: Long): Action[AnyContent] = tenantActionBuilder.require(Access.SellingPage.read).sync { implicit request =>
    logger.debug(s"[Transfer $transferId]: generating pdf...")

    val pdfFileBytes = {
      val jasperParams = buildJasperParameters(request, transferId)
      val jasperDataSource = buildJasperDataSource(transferId)
      val templateFile = JasperCompileManager.compileReport(environment.resourceAsStream("reports/danbredlabels.jrxml").get)
      JasperExportManager.exportReportToPdf(JasperFillManager.fillReport(templateFile, jasperParams, jasperDataSource))
    }

    logger.debug(s"[Transfer $transferId]: pdf generated. Sending to front-end...")

    Ok(pdfFileBytes)
      .as("application/pdf")
      .withHeaders("Content-Disposition" -> "inline;filename=labels.pdf")
  }

  private def asJavaMap(fields: Map[String, Object]): java.util.Map[String, Object] = new java.util.HashMap[String, AnyRef](fields.asJava)

  private def buildJasperDataSource(transferId: Long)(implicit request: WebActionRequest[AnyContent]) = {
    val animalsData = request.withSession.readOnly(implicit tt => fetchReportData(transferId)(tt.dbSession))

    if (animalsData.isEmpty) new JREmptyDataSource()
    else {
      def toPdfRow(locationOpt: Option[String], animalsInLocation: List[AnimalData]) = {
        animalsInLocation.map(_.animalId)
          .grouped(AnimalsPerRow)
          .toList
          .map(animalsInRow => ReportDataRow(locationOpt.getOrElse(""), animalsInRow))
      }

      val pdfDataRows = animalsData
        .groupBy(_.locationOpt).toList
        .flatMap { case (locationOpt, animals) => toPdfRow(locationOpt, animals) }

      logger.debug(
        s"[Transfer $transferId] data source: ${pdfDataRows.map(r => s"${r.location}: ${r.animalsInRow.size} animals").mkString(", ")}",
      )

      new MapJRDataSource(
        pdfDataRows
          .map(_.asJasperFieldsMap)
          .map(asJavaMap)
          .asJava,
      )
    }
  }

  private def buildJasperParameters(request: WebActionRequest[AnyContent], transferId: Long): java.util.Map[String, AnyRef] = {
    val commonParams = Map(
      JRParameter.REPORT_LOCALE -> request.lang.toLocale,
      JRParameter.REPORT_TIME_ZONE -> TimeZone.getTimeZone(request.user.getFarmTimezoneName),
      JRParameter.REPORT_FORMAT_FACTORY -> new CloudfarmsFormatFactory(),
      "PLAY_LANG" -> new Lang(request.lang),
      "messagesApi" -> messagesApi.asJava,
      "dateFormat" -> PdfUtils.farmShortDateFormat(request.lang, request.user),
    )

    val (farmDetails, sellingData) = request.withSession.readOnly { implicit tt =>
      import tt.implicits._
      (
        fetchFarmDetails(transferId),
        fetchSellingData(transferId),
      )
    }

    logger.debug(s"[Transfer $transferId] farm details: $farmDetails")
    logger.debug(s"[Transfer $transferId] selling data: $sellingData")

    asJavaMap(
      commonParams ++
        farmDetails.asJasperParams ++
        sellingData.asJasperParams,
    )
  }

  private def fetchReportData(transferId: Long)(implicit dbSession: DBSession) = {
    sql"""select
         |   coalesce(s.animalid, b.animalid, g.animalid) as animalid,
         |   l.name as location
         | from transferout tr
         |   inner join transferindividualout i on i.transferout_id = tr.id
         |   left join sow s on i.sow_id = s.id
         |   left join gilt g on i.gilt_id = g.id
         |   left join boar b on i.boar_id = b.id
         |   left join location l on i.location_id = l.id
         | where tr.id = $transferId
         |   and coalesce(s.animalid, b.animalid, g.animalid) is not null
         |"""
      .stripMargin
      .map(AnimalData(_))
      .list.apply()
  }

  private def fetchFarmDetails(transferId: Long)(implicit dbSession: DBSession) = {
    // Pick one of the locations of the individual animals in this transfer as starting point to find the farm location
    val animalLocationIdQuery = {
      sqls"""
            |select l.id
            |    from transferout tr
            |      inner join transferindividualout i on i.transferout_id = tr.id
            |      left join location l on i.location_id = l.id
            |    where tr.id = $transferId
            |    fetch first row only
        """
    }

    // Go up in locations hierarchy until the top location, this will be the farm
    val farmLocationIdQuery = {
      sqls"""
           |with recursive path as (
           |    select id, parent_id from location where id = ($animalLocationIdQuery)
           |    union all
           |    select t.id, t.parent_id from location t join path p on t.id = p.parent_id
           |)
           |select id from path where parent_id is null
           |"""
    }

    val farmDetails = {
      sql"""
           |select
           |  l.name, l.street1, l.street2, l.postalnumber, l.city, l.region, cn.name as country, l.farm_centralrn as farm_rn
           |  from location l
           |   left outer join country cn on l.country_id = cn.id
           |  where l.id = ($farmLocationIdQuery)
           |"""
        .stripMargin
        .map(FarmDetails(_))
        .single.apply()
    }

    farmDetails.getOrElse(sys.error(s"DB problem: not found farm location for transfer " + transferId))
  }

  private def fetchSellingData(transferId: Long)(implicit dbSession: DBSession) = {
    sql"""select
         |   tr.transfername as shipping_name,
         |   tr.blood_sample_date,
         |   b.companyname as customer_name
         | from transferout tr
         |   left outer join business b on tr.business_id = b.id
         | where tr.id = $transferId
         |"""
      .stripMargin
      .map(SellingData(_))
      .single.apply()
  }.getOrElse(sys.error("DB problem: no selling data"))
}

object DanbredLabelsJasperReport {
  val AnimalsPerRow: Int = 4

  case class SellingData(shippingName: String, bloodSampleDate: LocalDate, customerNameOpt: Option[String]) {
    def asJasperParams: Map[String, Object] = Map(
      "shippingName" -> shippingName,
      "customerName" -> customerNameOpt.getOrElse("-"),
      "bloodSampleDate" -> bloodSampleDate,
    )
  }

  object SellingData {
    def apply(rs: WrappedResultSet): SellingData = SellingData(
      rs.stringOpt("shipping_name").getOrElse(sys.error("No shipping name")), // must be filled before report is available
      rs.localDateOpt("blood_sample_date").getOrElse(sys.error("No blood sample date")), // must be filled before report is available
      rs.stringOpt("customer_name"),
    )
  }

  case class FarmDetails(
    name: String,
    street1Opt: Option[String],
    street2Opt: Option[String],
    postalNumOpt: Option[String],
    cityOpt: Option[String],
    regionOpt: Option[String],
    countryOpt: Option[String],
    farmRnOpt: Option[String],
  ) {
    def asJasperParams: Map[String, Object] = Map(
      "farmName" -> name,
      "farmInfo" -> farmAddress,
      "farmRn" -> farmRnOpt.getOrElse("-"),
    )

    def farmAddress: String = {
      // [street1] [street2]
      val line1opt = street1Opt.map(_ + " " + street2Opt.getOrElse(""))

      // [postalNumber] [city], [region]
      val line2opt = {
        def postalPrefix = postalNumOpt.map(_ + " ").getOrElse("")
        val cityWithPostalOpt = cityOpt.map(postalPrefix + _)
        regionOpt.map(region => cityWithPostalOpt.map(city => s"$city, $region").getOrElse(region))
          .orElse(cityWithPostalOpt)
      }

      List(line1opt, line2opt, countryOpt)
        .collect { case Some(text) => text }
        .mkString("\n")
    }
  }

  object FarmDetails {
    def apply(rs: WrappedResultSet): FarmDetails = FarmDetails(
      rs.stringOpt("name").getOrElse(sys.error("No farm name")),
      rs.stringOpt("street1"),
      rs.stringOpt("street2"),
      rs.stringOpt("postalnumber"),
      rs.stringOpt("city"),
      rs.stringOpt("region"),
      rs.stringOpt("country"),
      rs.stringOpt("farm_rn"),
    )
  }

  case class AnimalData(animalId: String, locationOpt: Option[String])

  object AnimalData {
    def apply(rs: WrappedResultSet): AnimalData = AnimalData(
      rs.string("animalid"), // assuming we filtered out nulls in the query
      rs.stringOpt("location"),
    )
  }

  case class ReportDataRow(location: String, animalsInRow: List[String]) {
    def asJasperFieldsMap: Map[String, Object] = {
      val animalsCount = animalsInRow.size
      if (animalsInRow.isEmpty)
        sys.error(s"Labels report: row with no animals")
      if (animalsCount > AnimalsPerRow)
        sys.error(s"Labels report: a row can contain $AnimalsPerRow animals, received $animalsCount")

      val animalsMap = (1 to animalsCount)
        .map(i => s"animalId_$i" -> animalsInRow.lift(i - 1).orNull)
        .toMap
      animalsMap + ("location" -> location)
    }
  }

}
