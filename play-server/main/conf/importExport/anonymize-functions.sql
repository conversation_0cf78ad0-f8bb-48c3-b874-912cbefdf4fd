
create or replace function public.base36_encode(digits bigint, min_width integer DEFAULT 0) returns character varying
    immutable
    language plpgsql
as
$$
DECLARE
    chars char[];
    ret varchar;
    val bigint;
BEGIN
    chars := ARRAY['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'];
    val := digits;
    ret := '';
    IF val < 0 THEN
        val := val * -1;
    END IF;
    WHILE val != 0 LOOP
            ret := chars[(val % 36)+1] || ret;
            val := val / 36;
        END LOOP;

    IF min_width > 0 AND char_length(ret) < min_width THEN
        ret := lpad(ret, min_width, '0');
    END IF;

    RETURN ret;
END;
$$;

alter function public.base36_encode(bigint, integer) owner to cloudfarms;
