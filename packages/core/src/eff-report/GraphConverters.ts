import { BarSeriesOption, EChartsOption, LineSeriesOption } from "echarts";
import { jsonrepair } from "jsonrepair";
import type { PieChartParams, StackedBarChartParams, StackedLineGraphParams } from "./ChartSchemas";

const colors = [
	"#5470C6", // Blue
	"#91CC75", // Green
	"#FF9F7F", // Coral
	"#FFBB6B", // Orange
	"#73C0DE", // Light Blue
	"#E062AE", // Pink
	"#7D3C98", // Purple
	"#52BE80", // Emerald
	"#F7DC6F", // Yellow
	"#E74C3C", // Red
];

function getTitle(params: { title: string }) {
	return {
		text: params.title,
		textStyle: {
			fontSize: 14,
		},
		left: "center",
	};
}

/**
 * Converts StackedLineGraphParams to ECharts option format using markLine for goals
 */
export const convertStackedLineGraphToECharts = (params: StackedLineGraphParams): EChartsOption => {
	const { yAxisName, yAxisUnit, series, periodNames } = params;

	if (!periodNames) {
		// eslint-disable-next-line no-console
		console.warn("periodNames is empty, x axis will not have right labels!");
	}

	const dataYMin = Math.min(...series.map((s) => Math.min(...s.data, s.goal ?? Infinity)));
	const dataYMax = Math.max(...series.map((s) => Math.max(...s.data, s.goal ?? -Infinity)));
	const range = dataYMax - dataYMin == 0 ? 1 : dataYMax - dataYMin; //If range is zero, we would divide by zero, so we set it to 1
	const exponent = Math.floor(Math.log10(range));
	const roundingFactor = Math.pow(10, -exponent);
	const axisYMinComputed = Math.floor((dataYMin - Math.max(range / 5, 0)) * roundingFactor) / roundingFactor;
	const axisYMin = dataYMin >= 0 ? Math.max(axisYMinComputed, 0) : axisYMinComputed;
	const axisYMax = Math.ceil((dataYMax + Math.max(range / 5, 0)) * roundingFactor) / roundingFactor;

	const eChartSeries = series.map((s, index) => {
		const seriesItem: LineSeriesOption = {
			name: s.title,
			type: "line",
			data: s.data,
			smooth: false,
			symbol: "circle",
			symbolSize: 8,
			emphasis: {
				focus: "series",
			},
			color: colors[index % colors.length],
			markLine:
				s.goal !== undefined && s.goal !== null
					? {
							silent: true,
							label: {
								formatter: `Goal: ${s.goal}`,
							},
							lineStyle: {
								type: "dashed",
								width: 2,
								color: colors[index % colors.length],
							},
							data: [
								{
									yAxis: s.goal,
									name: "Goal",
								},
							],
						}
					: undefined,
		};
		return seriesItem;
	});

	const seriesWithGoal = series.filter((s) => s.goal !== undefined && s.goal !== null);

	return {
		title: getTitle(params),
		tooltip: {
			trigger: "axis",
			// using any, because of really complicated types in echart.
			// tried to use CallbackDataParams | CallbackDataParams[], but it does not know about axisValueLabel.
			// tried TooltipCallbackDataParams, but it does not know other params.
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			formatter: function (params: any) {
				let result = `${params[0].axisValueLabel}<br/>`;
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				params.forEach((param: any) => {
					if (param.seriesType === "line") {
						result += `${param.marker} ${param.seriesName}: ${param.value}${yAxisUnit}<br/>`;
					}
				});

				if (seriesWithGoal.length > 0) {
					series.forEach((s, index) => {
						if (s.goal !== undefined && s.goal !== null) {
							result += `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${colors[index % colors.length]};"></span>`;
							result += `Goal ${s.title}: ${s.goal}<br/>`;
						}
					});
				}

				return result;
			},
		},
		legend: {
			data: eChartSeries.map((s) => s.name as string).filter((a) => a !== undefined),
			orient: "horizontal",
			bottom: 10,
		},
		/*		grid: {
			left: "5%",
			right: seriesWithGoal.length > 0 ? "14%" : "3%",
			bottom: "15%",
			top: "15%",
			containLabel: true,
		},*/
		xAxis: {
			type: "category",
			boundaryGap: false,
			data: periodNames,
		},
		yAxis: {
			type: "value",
			name: yAxisName + ` [${yAxisUnit}]`,
			min: axisYMin,
			max: axisYMax,
			nameLocation: "middle",
			nameGap: 40,
			axisLabel: {
				formatter: `{value}`,
			},
		},
		series: eChartSeries,
	};
};

export const convertPieChartToECharts = (params: PieChartParams): EChartsOption => {
	return {
		title: getTitle(params),
		tooltip: {
			trigger: "item",
			formatter: `{b}: {c} ({d}${params.seriesUnits === undefined ? "" : " " + params.seriesUnits})`,
		},
		legend: {
			orient: "horizontal",
			bottom: 10,
			data: params.series.map((item) => item.name),
		},
		series: [
			{
				type: "pie",
				radius: "50%",
				data: params.series.map((item, index) => ({
					...item,
					itemStyle: {
						color: colors[index % colors.length],
					},
				})),
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: "rgba(0, 0, 0, 0.5)",
					},
				},
				label: {
					formatter: `{b} ({c}${params.seriesUnits === undefined ? "" : " " + params.seriesUnits})`,
				},
			},
		],
	};
};

export const convertStackedBarChartToECharts = (params: StackedBarChartParams): EChartsOption => {
	const { yAxisName, yAxisUnit, series, periodNames } = params;

	const eChartSeries = series.map(
		(s, index) =>
			({
				name: s.title,
				type: "bar",
				stack: "total",
				emphasis: {
					focus: "series",
				},
				data: s.data,
				color: colors[index % colors.length],
			}) as BarSeriesOption,
	);

	return {
		title: getTitle(params),
		tooltip: {
			trigger: "axis",
			axisPointer: {
				type: "shadow",
			},
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			formatter: (params: any) => {
				let total = 0;
				let result = `${params[0].axisValueLabel}<br/>`;
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				params.forEach((param: any) => {
					total += param.value;
					result += `${param.marker} ${param.seriesName}: ${param.value}${yAxisUnit}<br/>`;
				});
				result += `<br/><b>Total: ${total}${yAxisUnit}</b>`;
				return result;
			},
		},
		legend: {
			data: series.map((s) => s.title),
			orient: "horizontal",
			bottom: 10,
		},
		...(series.length >= 6 && {
			grid: {
				bottom: "25%",
			},
		}),
		xAxis: {
			type: "category",
			data: periodNames,
		},
		yAxis: {
			type: "value",
			name: yAxisName + ` [${yAxisUnit}]`,
			nameLocation: "middle",
			nameGap: 40,
			axisLabel: {
				formatter: `{value}`,
			},
		},
		series: eChartSeries,
	};
};

export function convertJsonToGraphOpt(type: string, text: string, periodNames: string[]) {
	let convertedOptions: EChartsOption | undefined;
	let explanation: string | undefined;
	let title: string | undefined;
	let height = 300;

	// LLM sometimes returns broken json. This attempts to fix it.
	const options = JSON.parse(jsonrepair(text));

	try {
		if (type === "vfastackedlinegraph") {
			explanation = options.explanation;
			title = options.title;
			convertedOptions = convertStackedLineGraphToECharts(options);
			if (convertedOptions.series && (!Array.isArray(convertedOptions.series) || convertedOptions.series.length === 1)) {
				height = 250;
			}
		} else if (type === "piechart") {
			explanation = options.explanation;
			title = options.title;
			convertedOptions = convertPieChartToECharts(options);
		} else if (type === "stackedbarchart") {
			convertedOptions = convertStackedBarChartToECharts(options);
			explanation = options.explanation;
			title = options.title;
		} else {
			// eslint-disable-next-line no-console
			console.error("AI has generated not recognized code block: " + text);
		}
	} catch {
		// eslint-disable-next-line no-console
		console.error("Error generating graph to ECharts: \nGraph type: " + type + "\nGraph text: " + text);
	}

	return { title, convertedOptions, explanation, height };
}
