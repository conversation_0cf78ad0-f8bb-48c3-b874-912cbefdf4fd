import { convertEffReportToText } from "../data-to-text/DataToText";
import { IdentifiedIssue, IdentifyIssuesResponse } from "./IdentifyIssuesResponseSchema";
import { EfficiencyReportData } from "../EfficiencyReportData";
import { RenderedPromptTemplate, renderPromptTemplate } from "../prompt-template/PromptTemplate";
import { getSources } from "../source-docs/SourceDoc";
import { HoldingId, SystemHoldingId } from "../HoldingId";
import { PromptCustomization } from "../PromptCustomization";
import { EffReportPrompts } from "./EffReportPrompts";
import { SimpleChatMessage } from "./SimpleChatMessage";
import { GetRelevantDataResponse, relevantDataToText } from "../relevant-data/RelevantDataService";
import { getPeriodNames } from "../GetPeriodNames";
import { getLanguageLevelsContext } from "../language-level/getLanguageLevelsContext";
import { emptyUsage, sumUsages, textCompletion, Usage } from "../llm/LlmCommon";
import { removeHeading, splitByHorizontalRule } from "../utils/markdownUtils";
import { toGeminiSchema } from "gemini-zod";
import { pieChartSchema, stackedBarChartSchema, stackedLineGraphSchema } from "./ChartSchemas";

export const ISSUE_IDENTIFICATION_STEP = "issue-identification-step";

export type AnalyzeCausesInternalResponse = AnalyzeSingleIssueResponse[];

export type AnalyzeSingleIssueResponse = {
	issue: IdentifiedIssue;
	messages: SimpleChatMessage[];
	cause: string;
	analysis: string;
	usedData: string;
	summary: string;
	usage: Usage;
};

export async function analyzeCauses(params: AnalyzeCausesParams): Promise<AnalyzeCausesInternalResponse> {
	return await Promise.all(params.issuesResponse.issues.map((_, idx) => analyzeSingleIssue(idx, params)));
}

type AnalyzeCausesParams = {
	report: EfficiencyReportData;
	holdingId: HoldingId;
	introductionResponse: RenderedPromptTemplate;
	promptCustomization: PromptCustomization;
	relevantDataResponse: GetRelevantDataResponse;
	issuesResponse: IdentifyIssuesResponse;
};

/**
 * Analyze a single issue to identify the cause
 */
export async function analyzeSingleIssue(
	idx: number,
	{ report, holdingId, introductionResponse, promptCustomization, relevantDataResponse, issuesResponse }: AnalyzeCausesParams,
): Promise<AnalyzeSingleIssueResponse> {
	const sources = await getSources({ userLanguage: report.language, holdingId, relevantFor: [ISSUE_IDENTIFICATION_STEP] });

	const issue = issuesResponse.issues[idx];
	const otherIssues = issuesResponse.issues.filter((_, i) => i !== idx).map((i) => i);

	const periodNames = getPeriodNames(report).periodNames;

	// Load optional holding specific instructions
	const holdingInstructions = (
		await renderPromptTemplate({
			promptId: {
				promptType: EffReportPrompts.causeAnalysis_holdingInstructions,
				holdingId,
				templateOverrides: promptCustomization.templateOverrides,
			},
			optional: true,
		})
	)?.template.template;

	const relevantData = relevantDataToText(relevantDataResponse.relevantData, getPeriodNames(report));

	// Context is shared by all prompts
	const context = {
		...introductionResponse.context,
		// KPI codes are needed here, because they are expected in the response
		reportText: convertEffReportToText(report, false),
		...sources,
		holdingInstructions,
		...relevantData,
		issue,
		otherIssues,
		periodNames,
		latestPeriodName: periodNames[0],
		...(await getLanguageLevelsContext(promptCustomization.templateOverrides)),
		stackedLineGraphSchema: JSON.stringify(toGeminiSchema(stackedLineGraphSchema), null, 2),
		pieChartSchema: JSON.stringify(toGeminiSchema(pieChartSchema), null, 2),
		stackedBarChartSchema: JSON.stringify(toGeminiSchema(stackedBarChartSchema), null, 2),
	};

	// First, we ask to identify issues. This will be later reviewed, so we call it initial identification.
	const messages: SimpleChatMessage[] = [
		{
			role: "system",
			content: introductionResponse.content,
		},
		{
			role: "user",
			content: (
				await renderPromptTemplate({
					promptId: {
						promptType: EffReportPrompts.causeAnalysis_1_initialAnalysis,
						holdingId: SystemHoldingId,
						templateOverrides: promptCustomization.templateOverrides,
					},
					context,
				})
			).content,
		},
	];

	let usageSummary: Usage = emptyUsage({ activity: "eff-report", action: "analyze-causes" });

	const { response: initialIdentification, usage: usage1 } = await textCompletion({
		messages,
		metricData: usageSummary.metricData,
		cacheIdx: promptCustomization.cacheIdx,
	});

	usageSummary = usage1 ? sumUsages(usageSummary, usage1) : usageSummary;

	// Review the initial identification
	messages.push(
		// We add the previous response to the messages.
		{
			role: "assistant",
			content: initialIdentification,
		},
		{
			role: "user",
			content: (
				await renderPromptTemplate({
					promptId: {
						promptType: EffReportPrompts.causeAnalysis_2_review,
						holdingId: SystemHoldingId,
						templateOverrides: promptCustomization.templateOverrides,
					},
					context,
				})
			).content,
		},
	);

	const { response: review, usage: usage2 } = await textCompletion({
		messages,
		metricData: usageSummary.metricData,
		cacheIdx: promptCustomization.cacheIdx,
	});

	usageSummary = usage2 ? sumUsages(usageSummary, usage2) : usageSummary;

	// After the review, we ask AI to incorporate the feedback and provide the final identification
	messages.push(
		{
			role: "assistant",
			content: review,
		},
		{
			role: "user",
			content: (
				await renderPromptTemplate({
					promptId: {
						promptType: EffReportPrompts.causeAnalysis_3_final,
						holdingId: SystemHoldingId,
						templateOverrides: promptCustomization.templateOverrides,
					},
					context,
				})
			).content,
		},
	);

	const { response, usage: usage3 } = await textCompletion({
		messages: messages,
		metricData: {
			activity: "eff-report",
			action: "analyze-causes",
		},
		cacheIdx: promptCustomization.cacheIdx,
	});

	usageSummary = usage3 ? sumUsages(usageSummary, usage3) : usageSummary;

	messages.push({
		role: "assistant",
		content: response,
	});
	
	const parts = splitByHorizontalRule(response);

	return {
		analysis: parts[0],
		usedData: parts[1],
		cause: parts[2],
		summary: removeHeading(parts[3]),
		messages,
		issue,
		usage: usageSummary,
	};
}
