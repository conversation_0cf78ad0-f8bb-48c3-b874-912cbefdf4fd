Rewrite the analysis using the same instructions but incorporate the feedback from the review.
Respond in the following language: {{language}}.
The response should use terminology understandable to pig farmer from {{country}}.

Split your answer into the following sections:
- Analysis - Detailed analysis. Structure and format this section for high readability. Incorporate graphs directly alongside the relevant text to increase clarity. Add a graph every time you mention a trend or a value distribution. Make sure the text doesn't unnecessarily repeat what can be seen in the graph and that the two complement each other. {{WriteInExpertLanguageLevel}}
- Relevant data - Show input data that were crucial for analysis of the issue in table format. It's ok to use multiple tables.
- Identified cause - Write a summary of the analysis with a focus on identified cause as a single unstructured paragraph of text. If the exact cause can't be confirmed by the data, suggest what needs to be done to find the actual cause, such as having a veterinary visit the farm. Highlight important information in bold to increase readability. Don't suggest actions to resolve the issue. Keep this section short. {{WriteInStandardLanguageLevel}}
- One sentence summary - Write a one sentence summary of the identified cause section. Write this section without any additional comments. Just a single sentence without highlighted text. This sentence will be added to a document after a sentence that summarizes the issue. Make sure the sentences will flow nicely together and there is no unnecessary repetition. Don't include the issue summary here. Issue summary: {{issue.summary}}

Separate the sections by horizontal lines. Start each section with a level 1 heading. Don't add any other comments before or after these four sections.

To include a graph, add a markdown code block:
```graph_type
{
	foo: "bar"
}
```
Replace graph_type with an actual type of graph. Replace the example json with an actual json according to schema for the specific graph type.

Pie Chart:
Use this specifically for showing proportions or percentage breakdown of a whole.
graph_type: piechart
Schema:
{{pieChartSchema}}
End of Pie Chart definition.

Multi-Line Trend Graph:
Use this specific type to compare data series over 4 periods, with optional goal lines.
graph_type: vfastackedlinegraph
Schema:
{{stackedLineGraphSchema}}
End of Multi-Line Trend Graph definition.

Stacked Bar Chart:
Use this to show how a total amount is divided into parts across different categories or periods.
graph_type: stackedbarchart
Schema:
{{stackedBarChartSchema}}
End of Stacked Bar Chart definition.

General Graphing Guidance: When preparing multi-series line graphs (`vfastackedlinegraph`) or generic graphs that function like line or bar charts with multiple series:
1.  **Check for Scale Disparity:** Before generating a single graph with multiple data series, compare the approximate range or maximum values of the series.
2.  **Decision Rule:** If the maximum value of one data series is significantly larger (e.g., roughly **10 times or more**) than the maximum value of another series intended for the *same* graph, **you MUST generate separate graphs** instead of one combined graph.
3.  **Implementation:** Create one graph for the series with larger values and a separate graph for the series with smaller values. Ensure each graph has an appropriate Y-axis scale for the data it displays.
4.  **Rationale:** This is crucial for readability. Combining series with vastly different scales (like one averaging 2 and another averaging 80) makes the variation in the smaller-valued series impossible to see on a shared Y-axis. Separate graphs ensure that trends and variations in *all* data series are clearly visible.
5.  **Units:** Also, if series intended for the same graph have different units (e.g., 'kg' vs '%'), they should always be plotted on separate graphs.