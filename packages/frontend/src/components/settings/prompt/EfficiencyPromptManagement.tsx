/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import 'antd/lib/menu/style/index.less';
import 'antd/lib/tabs/style/index.less';
import { makeAutoObservable } from 'mobx';
import { observer, useLocalObservable } from 'mobx-react-lite';
import PromptEditor, { PromptEditorState } from './PromptEditor';
import Button from 'antd/lib/button';
import Collapse from 'antd/lib/collapse';
import 'antd/lib/collapse/style/index.less';
import { FeedbackData, ReportOrFeedbackSelector } from '@src/components/settings/prompt/ReportOrFeedbackSelector';
import { ReportDataViewer } from '@src/components/settings/prompt/ReportDataViewer';
import { ReportRequestData } from '@src/components/settings/prompt/ReportRequestCards';
import { Pending } from '@src/components/Pending';
import TrpcReact from '@src/common/TrpcReact';
import ReactJson from 'react-json-view';
import { LoadingError } from '@src/components/common/LoadingError';
import Card from 'antd/lib/card';
import Alert from 'antd/lib/alert';
import { GOOD_RESPONSE } from 'pigbot-core/src/Constants';
import { EffReportPrompts } from 'pigbot-core/src/eff-report/EffReportPrompts';
import { IntroductionPrompts } from 'pigbot-core/src/introduction/IntroductionPrompts';
import { StructuredPromptEditor } from '@src/components/settings/prompt/StructuredPromptEditor';
import { IdentifyIssuesResponseSchema } from 'pigbot-core/src/eff-report/IdentifyIssuesResponseSchema';
import { LanguageLevelPrompts } from 'pigbot-core/src/language-level/LanguageLevelPrompts';
import MarkdownStyle from '@src/components/common/MarkdownStyle';
import remarkGfm from 'remark-gfm';
import ReactMarkdown from 'react-markdown';
import { DataQualityPrompts } from 'pigbot-core/src/data-quality/DataQualityPrompts';

const { Panel } = Collapse;

export class PromptsState {
	promptEditorStatesMap: { [type: string]: PromptEditorState } = {};

	constructor() {
		makeAutoObservable(this);
	}

	getPromptEditorState(type: string) {
		if (!this.promptEditorStatesMap[type]) {
			this.promptEditorStatesMap[type] = new PromptEditorState();
		}
		return this.promptEditorStatesMap[type];
	}

	get getTemplateOverrides() {
		return Object.entries(this.promptEditorStatesMap).reduce(
			(result, [type, state]) => {
				if (state.selectedTemplateId !== Pending) {
					result[type] = state.selectedTemplateId;
				}
				return result;
			},
			{} as Record<string, string | null>,
		);
	}

	activate() {
		Object.values(this.promptEditorStatesMap).forEach((state) => {
			state.activate();
		});
	}

	get canActivate() {
		const keys = Object.keys(this.promptEditorStatesMap);
		// Collect all the prompt editor states for the current level.
		const promptEditorStates = keys.map((key) => this.promptEditorStatesMap[key]);
		return (
			// Can activate if prompt editor states are not in loading state and not in editing state.
			promptEditorStates.every((state) => !state.activateLoading && !state.isEditing) &&
			promptEditorStates.some((state) => state.isActive === false)
		);
	}
}

export class State {
	feedbackOrReports: 'feedback' | 'reports' | Pending = Pending;
	promptsState: PromptsState = new PromptsState();
	selected: ReportRequestData | FeedbackData | null = null;
	activePanel: string = 'summary';

	constructor() {
		makeAutoObservable(this);
	}

	setActivePanel = (key: string) => {
		this.activePanel = key;
	};

	setFeedbackOrReports = (at: 'feedback' | 'reports') => {
		this.selected = null;
		this.feedbackOrReports = at;
	};

	select = (selected: ReportRequestData | FeedbackData) => {
		this.selected = selected;
	};

	deselect() {
		this.selected = null;
	}
}

export const EfficiencyPromptManagement: React.FC<{ global: boolean }> = observer(({ global }) => {
	const state = useLocalObservable(() => new State());

	// Claude suggested making this a mutation even though it isn't a mutation.
	// This function is called on demand, and trpc supports this only for mutations.
	const compareValidations = TrpcReact.compareValidations.useMutation();

	const renderContent = () => {
		return (
			<div
				css={css`
					flex: 1;
					display: flex;
					flex-direction: row;
					overflow: hidden;
					gap: 10px;
				`}
			>
				<div
					css={css`
						display: flex;
						flex: 1;
						flex-direction: column;
						overflow: hidden;
					`}
				>
					<div
						css={css`
							overflow: auto;
							display: flex;
							flex-direction: column;

							.ant-collapse-header {
								padding-top: 8px !important;
								padding-bottom: 8px !important;
							}

							.ant-collapse-content-box {
								padding: 10px;
							}
						`}
					>
						{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
						<Collapse accordion={true} activeKey={state.activePanel} onChange={(key) => state.setActivePanel(key)}>
							{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
							<Panel key='introduction' header='Introduction'>
								<PromptEditor
									title='Prompt'
									promptsState={state.promptsState}
									templateType={IntroductionPrompts.prompt}
									isHoldingLevel={false}
									requirement={{
										type: 'mandatory',
									}}
								/>
								{!global && (
									<PromptEditor
										title='Holding specific instructions'
										promptsState={state.promptsState}
										templateType={IntroductionPrompts.prompt_holdingInstructions}
										isHoldingLevel={true}
										requirement={{
											type: 'optional',
											fallbackTemplate: null,
										}}
									/>
								)}
							</Panel>
							{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
							<Panel key='issue' header='1. Issue identification'>
								<PromptEditor
									title='1. Issue analysis'
									promptsState={state.promptsState}
									templateType={EffReportPrompts.issueIdentification_1_issue_analysis}
									isHoldingLevel={false}
									requirement={{
										type: 'mandatory',
									}}
								/>
								{!global && (
									<PromptEditor
										title='Holding specific instructions'
										promptsState={state.promptsState}
										templateType={EffReportPrompts.issueIdentification_holdingInstructions}
										isHoldingLevel={true}
										requirement={{
											type: 'optional',
											fallbackTemplate: null,
										}}
									/>
								)}
								<StructuredPromptEditor
									title='2. Extract top issues'
									promptsState={state.promptsState}
									templateType={EffReportPrompts.issueIdentification_2_top_issues}
									isHoldingLevel={false}
									schema={IdentifyIssuesResponseSchema}
								/>
							</Panel>
							{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
							<Panel key='cause' header='2. Cause analysis'>
								<PromptEditor
									title='1. Initial analysis'
									promptsState={state.promptsState}
									templateType={EffReportPrompts.causeAnalysis_1_initialAnalysis}
									isHoldingLevel={false}
									requirement={{
										type: 'mandatory',
									}}
								/>
								{!global && (
									<PromptEditor
										title='Holding specific instructions'
										promptsState={state.promptsState}
										templateType={EffReportPrompts.causeAnalysis_holdingInstructions}
										isHoldingLevel={true}
										requirement={{
											type: 'optional',
											fallbackTemplate: null,
										}}
									/>
								)}
								<PromptEditor
									title='2. Review'
									promptsState={state.promptsState}
									templateType={EffReportPrompts.causeAnalysis_2_review}
									isHoldingLevel={false}
									requirement={{
										type: 'mandatory',
									}}
								/>
								<PromptEditor
									title='3. Final prompt'
									promptsState={state.promptsState}
									templateType={EffReportPrompts.causeAnalysis_3_final}
									isHoldingLevel={false}
									requirement={{
										type: 'mandatory',
									}}
								/>
							</Panel>
							{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
							<Panel key='summary' header='3. Summary'>
								<PromptEditor
									title='Prompt'
									promptsState={state.promptsState}
									templateType={EffReportPrompts.summary}
									isHoldingLevel={false}
									requirement={{
										type: 'mandatory',
									}}
								/>
								{!global && (
									<PromptEditor
										title='Holding specific instructions'
										promptsState={state.promptsState}
										templateType={EffReportPrompts.summary_holdingInstructions}
										isHoldingLevel={true}
										requirement={{
											type: 'optional',
											fallbackTemplate: null,
										}}
									/>
								)}
							</Panel>
							{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
							<Panel key='language-levels' header='Language levels'>
								<PromptEditor
									title='Standard'
									promptsState={state.promptsState}
									templateType={LanguageLevelPrompts.standard}
									isHoldingLevel={false}
									requirement={{
										type: 'mandatory',
									}}
								/>
								<PromptEditor
									title='Expert'
									promptsState={state.promptsState}
									templateType={LanguageLevelPrompts.expert}
									isHoldingLevel={false}
									requirement={{
										type: 'mandatory',
									}}
								/>
							</Panel>
							{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
							<Panel key='conflicting-goals' header='Conflicting goals'>
								<PromptEditor
									promptsState={state.promptsState}
									templateType={DataQualityPrompts.detectConflictingGoal}
									isHoldingLevel={false}
									requirement={{
										type: 'mandatory',
									}}
								/>
							</Panel>

							{compareValidations.variables && (
								// @ts-expect-error antd 3x reports incorrect props with React 18
								<Panel
									key='validation'
									header='Prompt validation'
									css={css`
										.ant-collapse-content-box {
											display: flex;
											flex-direction: column;
											gap: 10px;
										}
									`}
								>
									{compareValidations.data ? (
										<>
											{compareValidations.data.brokenValidations.length === 0 ? (
												<Alert type='success' showIcon message='No feedback was broken with current prompts.' />
											) : (
												compareValidations.data.brokenValidations.map((validation) => (
													<Card
														key={validation.feedbackId}
														title={
															<>
																<div>{validation.summary}</div>
																<i>
																	Farm: {validation.farmName} At: {validation.createdAt} By: {validation.authorName}
																</i>
																<Button type='link'>Select</Button>
															</>
														}
														css={css`
															.ant-card-body {
																display: flex;
																flex-direction: column;
																gap: 10px;
															}
														`}
													>
														{validation.brokenResolutions.map((resolution) => {
															const isGoodResponse = resolution.newExplanation === GOOD_RESPONSE;

															return (
																<Card key={resolution.sectionId} size='small' title={<strong>Comment</strong>}>
																	<ReactMarkdown css={MarkdownStyle} remarkPlugins={[remarkGfm]}>
																		{resolution.sectionText}
																	</ReactMarkdown>
																	<Alert
																		type={isGoodResponse ? 'success' : 'warning'}
																		message={isGoodResponse ? 'Good response.' : resolution.feedback}
																		css={css`
																			white-space: break-spaces;
																		`}
																	/>
																	<strong>Resolution</strong>
																	<Alert message='Active prompt' description={resolution.oldExplanation} type='info' />
																	<Alert message='Current prompt' description={resolution.newExplanation} type='error' />
																</Card>
															);
														})}
													</Card>
												))
											)}

											<table
												css={css`
													border: 1px solid #dddddd;

													td,
													th {
														border: 1px solid #dddddd;
														padding: 8px;
													}
												`}
											>
												<thead>
													<tr>
														<th>Resolution</th>
														<th>Active Count</th>
														<th>New Count</th>
													</tr>
												</thead>
												<tbody>
													<tr>
														<td>Fully Resolved</td>
														<td>{compareValidations.data.activeResolutionsCount['fully-resolved']}</td>
														<td>{compareValidations.data.newResolutionsCount['fully-resolved']}</td>
													</tr>
													<tr>
														<td>Not Resolved</td>
														<td>{compareValidations.data.activeResolutionsCount['not-resolved']}</td>
														<td>{compareValidations.data.newResolutionsCount['not-resolved']}</td>
													</tr>
													<tr>
														<td>Unknown</td>
														<td>{compareValidations.data.activeResolutionsCount.unknown}</td>
														<td>{compareValidations.data.newResolutionsCount.unknown}</td>
													</tr>
												</tbody>
											</table>

											<ReactJson src={compareValidations.data} />
										</>
									) : (
										'Comparing active and current prompts. This may take a few minutes ...'
									)}
									{compareValidations.error && <LoadingError error={compareValidations.error.message} />}
								</Panel>
							)}
						</Collapse>
					</div>

					<div
						css={css`
							display: flex;
							justify-content: center;
							align-items: center;
							margin: 10px;
							gap: 15px;
						`}
					>
						<Button type='primary' onClick={() => state.promptsState.activate()} disabled={!state.promptsState.canActivate}>
							Activate
						</Button>
						<Button
							type='default'
							onClick={() => {
								compareValidations.mutate(state.promptsState.getTemplateOverrides);
								state.setActivePanel('validation');
							}}
							loading={compareValidations.isPending}
						>
							Validate current prompts
						</Button>
					</div>
				</div>

				{state.selected && state.feedbackOrReports ? (
					<ReportDataViewer
						selected={state.selected}
						templateOverrides={state.promptsState.getTemplateOverrides}
						deselect={() => state.deselect()}
					/>
				) : (
					<ReportOrFeedbackSelector state={state} global={global} />
				)}
			</div>
		);
	};

	return (
		<>
			<div
				css={css`
					flex: 1;
					display: flex;
					flex-direction: column;
				`}
			>
				{renderContent()}
			</div>
		</>
	);
});
