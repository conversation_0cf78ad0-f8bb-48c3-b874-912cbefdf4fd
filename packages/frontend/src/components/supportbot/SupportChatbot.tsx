/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useEffect, useState } from 'react';

import Button from 'antd/lib/button';
import 'antd/lib/button/style/index.less';

import Card from 'antd/lib/card';
import 'antd/lib/card/style/index.less';

import Input from 'antd/lib/input';
import 'antd/lib/input/style/index.less';

import 'antd/lib/checkbox/style/index.less';
import { observer } from 'mobx-react-lite';
import { makeAutoObservable } from 'mobx';
import TrpcReact from '@src/common/TrpcReact';
import { match, P } from 'ts-pattern';
import { SupportChatbotResponseChunk } from 'pigbot-core/src/supportbot/SupportChatbotTypes';
import rehypeExternalLinks from 'rehype-external-links';
import Alert from 'antd/lib/alert';
import Checkbox from 'antd/lib/checkbox';
import ReactMarkdown from 'react-markdown';
import MarkdownStyle from '@src/components/common/MarkdownStyle';
import remarkGfm from 'remark-gfm';
import { useUserContext } from '@src/components/Providers';
import '../../common/reset.less';

const { TextArea } = Input;

class State {
	isVisible: boolean = false;
	inputValue: string = '';
	threadId?: string = undefined;
	chatbotResponseMessages: ChatbotCard[] = [];
	responseBuffer: string = '';
	loading: boolean = false; //state between button/enter press and first AI message
	autoScroll: boolean = true;
	error?: string;
	annotationsText: string = '';

	sendToSlack: boolean = true;

	constructor() {
		makeAutoObservable(this);
	}

	toggleVisibility() {
		this.isVisible = !this.isVisible;
	}

	endQuery() {
		this.chatbotResponseMessages = [
			...this.chatbotResponseMessages,
			{ type: 'aiCard', message: this.responseBuffer + '\n' + this.annotationsText },
		];
		this.responseBuffer = '';
		this.annotationsText = '';
		this.inputValue = '';
	}

	clear() {
		this.chatbotResponseMessages = [];
		this.inputValue = '';
	}

	setLoading(loading: boolean) {
		this.loading = loading;
	}

	setThreadId(threadId: string) {
		this.threadId = threadId;
	}

	addToBuffer(data: string) {
		this.responseBuffer = this.responseBuffer + data;
	}

	addAnnotationToBuffer(annotationSummary: string) {
		this.annotationsText = annotationSummary;
	}

	setAutoscroll(autoscroll: boolean) {
		this.autoScroll = autoscroll;
	}

	commitUserMessage() {
		this.chatbotResponseMessages = [...this.chatbotResponseMessages, { type: 'userCard', message: this.inputValue }];
		this.inputValue = '';
	}

	setStreamError(error: string) {
		this.error = error;
	}

	setSendToSlack(sendToSlack: boolean) {
		this.sendToSlack = sendToSlack;
	}
}

type UserCard = {
	type: 'userCard';
	message: string;
};

type AICard = {
	type: 'aiCard';
	message: string;
};

type ChatbotCard = UserCard | AICard;
type SupportChatbotProps = {
	isChatbotReady: boolean;
	cfPath: string;
	helpUrl?: string;
};

/* eslint-disable @typescript-eslint/no-unused-vars */ //We will use the cfPath very soon. This should serve as reminder to implement it.
export const SupportChatbot: React.FC<SupportChatbotProps> = observer(({ isChatbotReady, cfPath, helpUrl }) => {
	const [state] = useState(() => new State());
	const chatContainerRef = React.useRef<HTMLDivElement>(null);
	/* eslint-disable @typescript-eslint/no-explicit-any*/
	//we must use any in here, because we are using ant 3.x and I was not able to make it work with any other type.
	const textareaRef = React.useRef<any>(null);

	const userContext = useUserContext();

	const getChatbotResponse = TrpcReact.supportChatbotService.useMutation();

	const focusTextarea = () => {
		if (state.isVisible) {
			if (textareaRef.current !== undefined) {
				textareaRef.current.focus();
			}
		}
	};

	async function executeStreamMutation() {
		try {
			if (getChatbotResponse.data === undefined) return;
			for await (const chunk of getChatbotResponse.data) {
				const typedChunk = chunk as SupportChatbotResponseChunk;
				match(typedChunk)
					.with({ type: 'queryStarted' }, () => {
						state.setLoading(true);
					})
					.with({ type: 'threadId' }, (data) => {
						state.setThreadId(data.threadId);
					})
					.with({ type: 'queryEnded' }, () => {
						state.endQuery();
					})
					.with(P.string, (data) => {
						if (state.loading) state.setLoading(false);
						state.addToBuffer(data);
					})
					.with({ type: 'annotationSummary' }, (annotation) => {
						state.addAnnotationToBuffer(annotation.annotationsSummary);
					})
					.exhaustive();
			}
		} catch (error) {
			state.setStreamError(String(error));
		}
	}

	useEffect(() => {
		executeStreamMutation();
	}, [getChatbotResponse.data]);

	useEffect(() => {
		if (chatContainerRef.current && state.autoScroll) {
			chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
		}
	}, [state.chatbotResponseMessages, state.loading, state.responseBuffer]);

	useEffect(() => {
		focusTextarea();
	}, [state.isVisible]);

	useEffect(() => {
		//set focus to textarea when the ai response has cleared. Can not be in state.endQuery(), because textarea is not yet enabled.
		if (state.responseBuffer === '') {
			focusTextarea();
		}
	}, [state.responseBuffer !== '']);

	const handleScroll = () => {
		if (chatContainerRef.current) {
			const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
			const isAtBottom = scrollTop + clientHeight >= scrollHeight - 5;
			// Enable auto-scroll if user is near the bottom
			state.setAutoscroll(isAtBottom);
		}
	};

	return (
		<div
			className='antd-reset support-chatbot-container' //support-chatbot-container was added to increase specificity of css, as it could be overriden by antd-reset.
			css={css`
				&.support-chatbot-container {
					z-index: 10;
					position: fixed;
					right: 0;
					bottom: 21px;
					display: flex;
					flex-direction: column;
					align-items: flex-end;
			`}
		>
			{state.isVisible && (
				<Card
					title={
						<div
							css={css`
								display: flex;
								flex-direction: row;
								gap: 10px;
							`}
						>
							<b
								css={css`
									flex: 1;
								`}
							>
								Support Chatbot
							</b>
							{userContext.role === 'backoffice' && (
								<Checkbox checked={state.sendToSlack} onChange={(e) => state.setSendToSlack(e.target.checked)}>
									Send to slack
								</Checkbox>
							)}
						</div>
					}
					size='small'
					css={css`
						width: 700px;
						margin-right: 10px;
						box-shadow:
							0 4px 8px rgba(0, 0, 0, 0.1),
							0 6px 20px rgba(0, 0, 0, 0.1);
					`}
				>
					<div
						css={css`
							min-height: 400px;
							max-height: 600px;
							overflow: auto;
							display: flex;
							flex-direction: column;
							gap: 1rem;
							${state.chatbotResponseMessages.length === 0 ? 'justify-content: center' : ''}
						`}
						onScroll={handleScroll}
						ref={chatContainerRef}
					>
						{state.chatbotResponseMessages.length === 0 && (
							<div
								css={css`
									text-align: center;
								`}
							>
								<p>Hello there!</p>
								<p
									css={css`
										margin-bottom: 0;
									`}
								>
									I’m the Cloudfarms chatbot.
								</p>
								<p
									css={css`
										margin-top: 0;
									`}
								>
									You can ask me about things like setting up new locations or how to create medicine prescriptions and much more.
								</p>
								<p>How can I help you today?</p>
								<p
									css={css`
										margin-top: 4em;
									`}
								>
									If you would rather try to find answers on your own, you can refer to{' '}
									<a href={helpUrl} target='_blank' rel='noreferrer'>
										our documentation.
									</a>
								</p>
							</div>
						)}
						{state.chatbotResponseMessages.map((messageCard, index) => (
							<Card
								key={index}
								css={css`
									width: 670px;
									background-color: ${messageCard.type == 'userCard' ? '#f0f0f0' : 'white'};
									word-wrap: break-word;
									${messageCard.type == 'userCard' ? 'white-space: break-spaces;' : ''}
								`}
								size={'small'}
							>
								<ReactMarkdown
									css={MarkdownStyle}
									remarkPlugins={[remarkGfm]}
									rehypePlugins={[[rehypeExternalLinks, { target: '_blank' }]]} // all links open in new tab
								>
									{messageCard.message}
								</ReactMarkdown>
							</Card>
						))}
						{state.loading && (
							<Card
								key={state.chatbotResponseMessages.length}
								css={css`
									width: 670px;
									word-wrap: break-word;
								`}
								size={'small'}
							>
								Loading...
							</Card>
						)}
						{state.responseBuffer != '' && (
							<Card
								key={state.chatbotResponseMessages.length}
								css={css`
									width: 670px;
									word-wrap: break-word;
								`}
								size={'small'}
							>
								<ReactMarkdown css={MarkdownStyle} remarkPlugins={[remarkGfm]}>
									{state.responseBuffer}
								</ReactMarkdown>
							</Card>
						)}
						{getChatbotResponse.error && <Alert message={getChatbotResponse.error.message} type='error' showIcon />}
						{state.error && <Alert message={state.error} type='error' showIcon />}
					</div>
					<div
						css={css`
							display: flex;
							gap: 8px;
							padding-top: 12px;
						`}
					>
						<TextArea
							ref={textareaRef}
							css={css`
								flex-grow: 1;
								min-height: 58px !important;
							`}
							placeholder={
								state.loading || state.responseBuffer !== ''
									? 'Loading...'
									: isChatbotReady
										? 'Type your question here'
										: 'The chatbot files is not ready yet. You can ask your question, but the answers might not be satisfactory.'
							}
							value={state.inputValue}
							onChange={(e) => (state.inputValue = e.target.value)}
							disabled={state.loading || state.responseBuffer !== ''}
							onPressEnter={(e) => {
								if (e.shiftKey) {
									return; // Do nothing if Shift is pressed with Enter
								}
								getChatbotResponse.mutate({
									message: state.inputValue,
									threadId: state.threadId,
									sendToSlack: state.sendToSlack,
								});
								state.commitUserMessage();
							}}
							autosize={{ minRows: 2, maxRows: 4 }}
						/>
						<div
							css={css`
								display: flex;
								flex-direction: column;
								gap: 2px;

								button {
									margin-bottom: 0; //Fix for CF styles. If modified, check it in CF also.
								}
							`}
						>
							<Button
								className='support-chatbot-clear-button'
								css={css`
									&.support-chatbot-clear-button:disabled {
										color: rgba(0, 0, 0, 0.25) !important;
										background-color: #f5f5f5 !important;
									}
								`}
								onClick={() => {
									state.clear();
								}}
								size={'small'}
								disabled={state.loading || state.responseBuffer !== ''}
							>
								Clear
							</Button>
							<Button
								className='support-chatbot-submit-button'
								css={css`
									&.support-chatbot-submit-button:disabled {
										color: rgba(0, 0, 0, 0.25) !important;
										background-color: #f5f5f5 !important;
									}
								`}
								type='primary'
								onClick={() => {
									getChatbotResponse.mutate({ message: state.inputValue, threadId: state.threadId, sendToSlack: state.sendToSlack });
									state.commitUserMessage();
								}}
								disabled={state.loading || state.responseBuffer !== ''}
							>
								Submit
							</Button>
						</div>
					</div>
				</Card>
			)}
			<div
				css={css`
					margin: 10px !important;

					.ant-btn-primary {
						background: #ffa500;
						background-color: #ffa500;
						border-color: #ffa500;
					}
				`}
			>
				<Button
					title='Support chatbot'
					type='primary'
					icon='question-circle'
					shape='circle'
					size='large'
					onClick={() => state.toggleVisibility()}
				/>
			</div>
		</div>
	);
});
