A microservice that exposes cloud farms to pigbot

## Tests:

### Easy snapshot tests:
This microservice mostly loads data from the database. 
The easiest way to verify the output is to compare it with a snapshot.
We are using this library: https://siriusxm.github.io/snapshot4s/file-snapshots
At the time of writing this, it only supported string inputs, so use pprint to convert the output to a string.

Creating or updating snapshots:
```
sbt snapshot4sPromote
```


## Build Image Locally:

Ensure that the Docker daemon is running.

1. Execute `sbt clean stage`
2. Build Docker images:
   ```docker buildx build -f Dockerfile-kpi-calculator -t kpi-calculator .```

## Run kpi-calculator Locally:
1. `sbt -Dconfig.file=config/application.conf`
2. `sbt project kpi-calculator run`
3. From the project root directory: `docker compose up`

## Run kpi-matrix
The project is a utility project, so no docker file is built.   

configuration for run is located in `config/application.json`

For farmId 3, run the following commands:

1. `sbt project kpi-matrix run matrix 3 matrix.json`
2. `sbt project kpi-matrix run descriptions 3 descriptions.csv`

