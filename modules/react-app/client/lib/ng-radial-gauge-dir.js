/* global d3 */
/*
 ng-radial-gauge 1.0.2
 (c) 2010-2014 <PERSON><PERSON><PERSON><PERSON>, 
 https://github.com/stherrienaspnet/ngRadialGauge
 License: MIT

 Version 1.0.2
 Author: <PERSON>
 Modified to add viewBox and use width attribute for scaling of SVG
 Removed flicker issue when using data=option mode.
 */
define(['angular', 'jquery', 'messages', 'cf/utils'], (function (ng, $, M, CF) {
  'use strict';
  var mod = angular.module('ngRadialGauge', []);

  mod.run(['$rootScope', '$window', function ($rootScope, $window) {
    angular.element($window).bind('resize', function () {
      $rootScope.$emit('resizeGaugeMsg');
    });
  }]);

  mod.factory('d3Service', ['$document', '$window', '$q', '$rootScope', function ($document, $window, $q, $rootScope) {
    var d = $q.defer(),
      d3service = {
        d3: function () {
          return d.promise;
        }
      };

    function onScriptLoad() {
      // Load client in the browser
      $rootScope.$apply(function () {
        d.resolve($window.d3);
      });
    }

    var scriptTag = $document[0].createElement('script');
    scriptTag.type = 'text/javascript';
    scriptTag.async = true;
    scriptTag.src = 'd3.min.js';
    scriptTag.onreadystatechange = function () {
      if (this.readyState === 'complete') onScriptLoad();
    };
    scriptTag.onload = onScriptLoad;

    var s = $document[0].getElementsByTagName('body')[0];
    s.appendChild(scriptTag);

    return d3service;
  }]);

  mod.directive('ngRadialGauge', ['$window', '$timeout', '$rootScope', function ($window, $timeout, $rootScope) {
    return {
      restrict: 'EAC',
      scope: {
        data: '=',
        widthRatio: '=',
        heightRatio: '=',
        lowerLimit: '=',
        upperLimit: '=',
        ranges: '=',
        value: '=',
        valueUnit: '=',
        precision: '=',
        majorGraduationPrecision: '=',
        label: '@',
        onClick: '&'
      },
      link: function (scope, ele, attrs) {
        var defaultUpperLimit = 100;
        var defaultLowerLimit = 0;
        var initialized = false;

        var renderTimeout;
        var gaugeAngle = parseInt(attrs.angle) || 120;

        //New width variable, now works in conjunction with fixed viewBox sizing
        var _width = attrs.width || '100%';

        /* Colin Bester
         Width and height are not really such an issue with SVG but choose these values as
         width of 300 seems to be pretty baked into code.
         I took the easy path seeing as size is not that relevant and hard coded width and height
         as I was too lazy to dig deep into code.
         May be the wrong call, but seems safe option.
         */
        var view;
        var majorGraduationTextColor = attrs.majorGraduationTextColor || '#333333';
        var inactiveColor = '#D7D7D7';
        var majorGraduationTextSize = parseInt(attrs.majorGraduationTextSize);
        var needleValueTextSize = parseInt(attrs.needleValueTextSize);
        var needleColor = attrs.needleColor || '#333333';
        var needle;
        var innerRadius;

        var outerRadius;
        var majorGraduationLength;
        var majorGraduationMarginTop;
        var valueVerticalOffset;

        var setView = function(ele) {
          var w = ele.width();
          view = {
            width: w,
            height: w * 0.75
          };
        }

        var initParams = function(ele) {
          setView(ele);
          innerRadius = Math.round((view.width * 100) / 300);
          outerRadius = Math.round((view.width * 130) / 300);
          majorGraduationLength = Math.round((view.width * 30) / 300);
          majorGraduationMarginTop = Math.round((view.width * -15) / 300);
          valueVerticalOffset = Math.round((view.width * 70) / 300);
        };

        initParams(ele);

        //The scope.data object might contain the data we need, otherwise we fall back on the scope.xyz property
        var extractData = function (prop) {
          if (!scope.data) return scope[prop];
          if (scope.data[prop] === undefined || scope.data[prop] === null) {
            return scope[prop];
          }
          return scope.data[prop];
        };

        var maxLimit;
        var minLimit;
        var value;
        var valueUnit;
        var precision;
        var majorGraduationPrecision;
        var ranges;
        var widthRatio;

        var updateInternalData = function () {
          maxLimit = extractData('upperLimit') ? extractData('upperLimit') : defaultUpperLimit;
          minLimit = extractData('lowerLimit') ? extractData('lowerLimit') : defaultLowerLimit;
          value = extractData('value');
          valueUnit = extractData('valueUnit');
          precision = extractData('precision');
          majorGraduationPrecision = extractData('majorGraduationPrecision');
          ranges = extractData('ranges');
          widthRatio = extractData('widthRatio');
        };
        updateInternalData();

        /* Colin Bester
         Add viewBox and width attributes.
         Used view.width and view.height in case it's decided that hardcoding these values is an issue.
         Width can be specified as %, px etc and will scale image to fit.
         */
        var _height = '75%';
        var ratioWidth = 100 / parseFloat(widthRatio);
        var svg = d3.select(ele[0])
          .append('svg')
          .attr('height',_height)
          .attr('width', _width)
          .attr('viewBox', '0 0 ' + view.width/ratioWidth + ' ' + view.height/ratioWidth);
        // .attr('view.width', view.width)
        // .attr('height', view.width * 0.75);

        var getMajorGraduationValues = function (pMinLimit, pMaxLimit) {
          var scaleRange = pMaxLimit - pMinLimit;
          var majorGraduationValues = [pMinLimit];

          ranges.forEach(function (pValue, i) {
            var scaleValue = pValue.max;
            majorGraduationValues.push(scaleValue);
          });

          return majorGraduationValues;
        };
        var getMajorGraduationAngles = function () {
          var scaleRange = 2 * gaugeAngle;
          var minScale = -1 * gaugeAngle;
          var graduationsAngles = [minScale];

          ranges.forEach(function (pValue, i) {
            var scaleValue = minScale + (pValue.max-minLimit)/(maxLimit-minLimit)*scaleRange;
            graduationsAngles.push(scaleValue);
          });
          return graduationsAngles;
        };
        var getNewAngle = function (pValue) {
          var scale = d3.scale.linear().range([0, 1]).domain([minLimit, maxLimit]);
          var ratio = scale(pValue);
          var scaleRange = 2 * gaugeAngle;
          var minScale = -1 * gaugeAngle;
          var newAngle = minScale + (ratio * scaleRange);
          return newAngle;
        };
        var renderMajorGraduationTexts = function (majorGraduationsAngles, majorGraduationValues, pValueUnit) {
          if (!ranges) return;

          var centerX = view.width / 2;
          var centerY = view.width / 2;
          var textVerticalPadding = 5;
          var textHorizontalPadding = 5;

          var lastGraduationValue = majorGraduationValues[majorGraduationValues.length - 1];
          var textSize = isNaN(majorGraduationTextSize) ? (view.width * 20) / 300 : majorGraduationTextSize;
          var fontStyle = textSize + 'px Courier';

          var dummyText = svg.append('text')
            .attr('x', centerX)
            .attr('y', centerY)
            .attr('fill', 'transparent')
            .attr('text-anchor', 'middle')
            .attr('font-size',textSize)
            .style('font', fontStyle)
            .text(lastGraduationValue + pValueUnit);

          var textWidth = dummyText.node().getBBox().width;

          for (var i = 0; i < majorGraduationsAngles.length; i++) {
            var angle = majorGraduationsAngles[i];
            var cos1Adj = Math.round(Math.cos((90 - angle) * Math.PI / 180) * (innerRadius - majorGraduationMarginTop - majorGraduationLength - textHorizontalPadding));
            var sin1Adj = Math.round(Math.sin((90 - angle) * Math.PI / 180) * (innerRadius - majorGraduationMarginTop - majorGraduationLength - textVerticalPadding));

            var sin1Factor = 1;
            if (sin1Adj < 0) sin1Factor = 1.1;
            if (sin1Adj > 0) sin1Factor = 0.9;
            if (cos1Adj > 0) {
              if (angle > 0 && angle < 45) {
                cos1Adj -= textWidth / 2;
              } else {
                cos1Adj -= textWidth;
              }
            }
            if (cos1Adj < 0) {
              if (angle < 0 && angle > -45) {
                cos1Adj -= textWidth / 2;
              }
            }
            if (cos1Adj === 0) {
              cos1Adj -= angle === 0 ? textWidth / 4 : textWidth / 2;
            }

            var x1 = centerX + cos1Adj;
            var y1 = centerY + sin1Adj * sin1Factor * -1;

            svg.append('text')
              .attr('class', 'mtt-majorGraduationText')
              .style('font', fontStyle)
              .attr('font-size',textSize)
              .attr('text-align', 'center')
              .attr('x', x1)
              .attr('dy', y1)
              .attr('fill', majorGraduationTextColor)
              .text(majorGraduationValues[i] + pValueUnit);
          }
        };
        var renderGraduationNeedle = function (value, valueUnit, precision, minLimit, maxLimit) {
          svg.selectAll('.mtt-graduation-needle').remove();
          svg.selectAll('.mtt-graduationValueText').remove();
          svg.selectAll('.mtt-graduation-needle-center').remove();

          var centerX = view.width / 2;
          var centerY = view.width / 2;
          var centerColor;

          if (typeof value === 'undefined') {
            centerColor = inactiveColor;
          } else {
            centerColor = needleColor;
            var nValue = (value <= maxLimit) ? value >= minLimit ? value : minLimit - (maxLimit-minLimit)*0.05 : maxLimit + (maxLimit-minLimit)*0.05;
            var needleAngle = getNewAngle(nValue);
            var needleLen = innerRadius - majorGraduationLength - majorGraduationMarginTop;
            var needleRadius = (view.width * 3.5) / 300;
            var textSize = isNaN(needleValueTextSize) ? (view.width * 30) / 300 : needleValueTextSize;
            var fontStyle = textSize + 'px Arial Narrow';

            var lineData = [
              [needleRadius, 0],
              [0, -needleLen],
              [-needleRadius, 0],
              [needleRadius, 0]
            ];
            var pointerLine = d3.svg.line().interpolate('monotone');
            var pg = svg.append('g').data([lineData])
              .attr('class', 'mtt-graduation-needle')
              .style('fill', needleColor)
              .attr('transform', 'translate(' + centerX + ',' + centerY + ')');
            needle = pg.append('path')
              .attr('d', pointerLine)
              .attr('transform', 'rotate(' + needleAngle + ')');


            svg.append('text')
              .attr('x', centerX)
              .attr('y', centerY + valueVerticalOffset)
              .attr('class', 'mtt-graduationValueText')
              .attr('fill', needleColor)
              .attr('text-anchor', 'middle')
              .attr('font-weight', 'bold')
              .style('font', fontStyle)
              .text(value + valueUnit);
          }

          var circleRadius = (view.width * 6) / 300;

          svg.append('circle')
            .attr('r', circleRadius)
            .attr('cy', centerX)
            .attr('cx', centerY)
            .attr('fill', centerColor)
            .attr('class', 'mtt-graduation-needle-center');
        };

        $rootScope.$on('resizeGaugeMsg', function (e) {
          $timeout(function () {
          });
        });

        /* Colin Bester
         Removed watching of data.value as couldn't see reason for this, plus it's the cause of flicker when using
         data=option mode of using directive.
         I'm assuming that calling render function is not what was intended on every value update.
         */
        // scope.$watchCollection('[ranges, data.ranges, data.value]', function () {
        scope.$watchCollection('[ranges, data.ranges]', function () {
          scope.render();
        }, true);


        scope.render = function () {
         initParams(ele);
          svg.selectAll('*').remove();
          if (renderTimeout) clearTimeout(renderTimeout);

          renderTimeout = $timeout(function () {
            var d3DataSource = [];

            if (typeof ranges === 'undefined') {
              d3DataSource.push([minLimit, maxLimit, inactiveColor]);
            } else {
              //Data Generation
              ranges.forEach(function (pValue, index) {
                d3DataSource.push([pValue.min, pValue.max, pValue.color]);
              });
            }

            //Render Gauge Color Area
            var translate = 'translate(' + view.width / 2 + ',' + view.width / 2 + ')';
            var cScale = d3.scale.linear().domain([minLimit, maxLimit]).range([-1 * gaugeAngle * (Math.PI / 180), gaugeAngle * (Math.PI / 180)]);
            var arc = d3.svg.arc()
              .innerRadius(innerRadius)
              .outerRadius(outerRadius)
              .startAngle(function (d) {
                return cScale(d[0]);
              })
              .endAngle(function (d) {
                return cScale(d[1]);
              });
            svg.selectAll('path')
              .data(d3DataSource)
              .enter()
              .append('path')
              .attr('d', arc)
              .style('fill', function (d) {
                return d[2];
              })
              .attr('transform', translate);

            var majorGraduationsAngles = getMajorGraduationAngles();
            var majorGraduationValues = getMajorGraduationValues(minLimit, maxLimit);
            renderMajorGraduationTexts(majorGraduationsAngles, majorGraduationValues, valueUnit);
            renderGraduationNeedle(value, valueUnit, precision, minLimit, maxLimit);
            initialized = true;
          }, 200);

        };
      }
    };
  }]);
}()));
