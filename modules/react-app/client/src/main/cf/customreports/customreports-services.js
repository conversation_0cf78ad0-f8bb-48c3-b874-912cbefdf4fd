/*global define*/

define(['./customreports-module', 'angular', 'jquery', 'messages', 'cf/utils', 'farmsettings', 'moment', 'slick.core', 'comp/data', 'columns'], function (mod, ng, $, M, CF, FS, moment, Slick) {
  'use strict';

  CF.addIfNotIn(mod.requires, ['data', 'columns', 'settings']);
  /* jshint latedef:nofunc */
  return mod
    .factory('CustomReportResource', ['$http', function ($http) {
      function CustomReportResource(url) {
        var self = this, dataVersion = 0, data = {}, goalData = {}, loading = false, forceReloadOption = false, activeData = [], currentParams = {};
        this.columns = [];
        this.options = [];
        this.invalidator = {};
        this.toParamStr = ng.toJson;
        this.calculationDate = new Date();

        this.getDataVersion = function () {
          return dataVersion;
        };

        this.data = {
          getItem: function (i) {
            if (i < activeData.length) {
              return activeData[i];
            } else {
              return null;
            }
          },
          getLength: function () {
            return activeData.length;
          },
          getItemMetadata: function (index) {
            if (activeData[index] && activeData[index].isHeader) {
              return {
                'cssClasses': 'report-section-header',
                'columns': {
                  0: {
                    'colspan': '*'
                  }
                }
              };
            } else {
              return null;
            }
          },
          resetData: function () {
            activeData = [];
          }
        };
        this.isLoading = function () {
          return loading;
        };

        this.canForceReload = function () {
          return forceReloadOption;
        };
        this.setNewLineIdsFromSectionParams = function (params) {
          activeData = [];
          if (!ng.isArray(params)) {
            console.error('sections params is not an array');
            return;
          }
          for (var i = 0; i < params.length; i++) {
            var section = params[i];
            if (ng.isUndefined(section.name) || ng.isUndefined(section.rows) || !ng.isArray(section.rows)) {
              console.error('missing name or rows or not array');
              return;
            }
            activeData.push({lineId: section.name, isHeader: true});
            for (var j = 0; j < section.rows.length; j++) {
              activeData.push({lineId: section.rows[j].code});
            }
          }
        };

        this.refreshActiveData = function (params) {
          if (!ng.isFunction(self.toParamStr)) {
            console.log('missing function for parameter object to string - using ng.toJson ');
            self.toParamStr = ng.toJson;
          }
          if (!params.columns || !params.sections || !params.global) {
            console.error('wrong params format:' + params);
            return;
          }
          var jsonParams = self.toParamStr(params);
          if (self.hasReportByParams(params)) {
            activeData = data[jsonParams].report;
            self.calculationDate = data[jsonParams].calculationDate;
            forceReloadOption = true;
          } else {
            forceReloadOption = false;
            self.setNewLineIdsFromSectionParams(params.sections);
          }
          dataVersion++;
        };


        this.load = function (params, forceReload, destination, processResponse) {
          loading = true;
          forceReloadOption = false;

          if (!ng.isFunction(self.toParamStr)) {
            console.log('missing function for parameter object to string - using ng.toJson ');
            self.toParamStr = ng.toJson;
          }

          var jsonParams = self.toParamStr(params);

          if (ng.isUndefined(data[jsonParams]) || $.isEmptyObject(data[jsonParams]) || forceReload) {
            $http.get(url + '?data=' + jsonParams).success(
              function (response) {
                loading = false;
                if (ng.isDefined(response.report)) {
                  data[jsonParams] = {report: response.report, calculationDate: response.calculationDate};
                  activeData = data[jsonParams].report;
                  self.calculationDate = data[jsonParams].calculationDate;
                } else {
                  console.log('report data in  response: ' + response + ' is missing');
                }

                if (ng.isObject(destination)) {
                  destination.report = data[jsonParams].report;
                  destination.calculationDate = data[jsonParams].report;
                  destination.jsonParams = jsonParams;
                } else {
                  console.log('rawData' + destination + ' is not an Object');
                }
                if (ng.isFunction(processResponse)) {
                  processResponse();
                } else {
                  console.log('processResponse ' + processResponse + ' is not an Function');
                }
                forceReloadOption = true;
                dataVersion++;
                for (var i = 0; i < activeData.length; i++) {
                  self.invalidator.invalidateRow(i);
                }
              }).error(function (response) {
              if (ng.isFunction(processResponse)) {
                processResponse(true);
              } else {
                console.log('processResponse ' + processResponse + ' is not an Function');
              }
              console.log(response);
              forceReloadOption = true;
            });
          } else {
            loading = false;
            activeData = data[jsonParams].report;
            self.calculationDate = data[jsonParams].calculationDate;

            if (ng.isObject(destination)) {
              destination.report = data[jsonParams].report;
              destination.calculationDate = data[jsonParams].calculationDate;
              destination.jsonParams = jsonParams;

            } else {
              console.log('rawData' + destination + ' is not an Object');
            }
            if (ng.isFunction(processResponse)) {
              processResponse();
              dataVersion++;
            } else {
              console.log('processResponse ' + processResponse + ' is not an Function');
            }
            forceReloadOption = true;
            dataVersion++;
          }

        };

        this.getReportByParams = function (params) {
          if (!ng.isFunction(self.toParamStr)) {
            console.log('missing function for parameter object to string ');
            self.toParamStr = ng.toJson;
          }
          return data[self.toParamStr(params)];
        };
        this.hasReportByParams = function (params) {
          if (!ng.isFunction(self.toParamStr)) {
            console.log('missing function for parameter object to string ');
            self.toParamStr = ng.toJson;
          }
          var jsonParams = self.toParamStr(params);
          return data[jsonParams] && ng.isArray(data[jsonParams].report) && data[jsonParams].report.length > 0;
        };


        this.exposedData = data;//TODO:remove after testing
      }

      return CustomReportResource;
    }])
    .factory('CustomReportData', ['$resource', 'CustomReportResource',
      function ($resource, CustomReportResource) {
        return new CustomReportResource('/api/reports/customreport/json');
      }
    ])
    .factory('CustomReportSettings', ['CustomReportColumnsUtils', 'CustomReportPeriodDefinition', '$state',
      function (CRColumnsUtils, CRPeriodDefinition, $state) {
        return {
          serialize: serialize,
          deserialize: deserialize,
          reset: reset,
          isInvalid: isInvalid
        };

        function isInvalid(settings, source) {
          var requiredArrays = ['columns', 'sections'];
          var requiredObjects = ['global'], i;
          for (i = 0; i < requiredArrays.length; i++) {
            if (!ng.isArray(settings[requiredArrays[i]])) {
              console.log('settings from - ' + source + ' - invalid ' + requiredArrays[i], settings);
              return true;
            }
          }
          for (i = 0; i < requiredObjects.length; i++) {
            if (!ng.isObject(settings[requiredObjects[i]])) {
              console.log('settings from - ' + source + ' - invalid ' + requiredObjects[i], settings);
              return true;
            }
          }
          return false;
        }

        function serialize(settingsFromScreen, stateData) {
          if (isInvalid(settingsFromScreen, 'url')) {
            settingsFromScreen = reset(settingsFromScreen);
          }
          var res = {};
          res.columns = settingsFromScreen.columns;
          res.sections = settingsFromScreen.sections;
          res.global = settingsFromScreen.global;
          if ($state.current.data && ng.isFunction($state.current.data.viewSettingsSerializeCallback)) {
            $state.current.data.viewSettingsSerializeCallback(res);
          }
          return res;
        }

        function deserialize(target, settingsFromUrl, stateData) {
          if (isInvalid(settingsFromUrl, 'url')) {
            settingsFromUrl = reset(settingsFromUrl);
          }
          var correctColDefs = CRPeriodDefinition.filterMalformed(settingsFromUrl.columns);

          var res = {};
          var gridColumnsFilters = CRColumnsUtils.syncFromSettings(stateData.customColumns, correctColDefs, stateData.customFilters, settingsFromUrl.global);
          stateData.customColumns = gridColumnsFilters.gridColumns;
          stateData.customFilters = gridColumnsFilters.filters;

          res.columns = correctColDefs;
          res.sections = settingsFromUrl.sections;
          res.global = settingsFromUrl.global;

          if ($state.current.data && ng.isFunction($state.current.data.viewSettingsDeserializeCallback)) {
            $state.current.data.viewSettingsDeserializeCallback(res);
          }
          return res;
        }


        function reset(settings) {
          var res = {};
          res.columns = [
            {id: 'c1', tp: 'kg'},
            {id: 'c2', tp: 'st', p: 1, pu: 'st', t: 0},
            {id: 'c3', tp: 'st', p: 1, pu: 'st', t: 1},
            {id: 'c4', tp: 'st', p: 1, pu: 'st', t: 2},
            {id: 'c5', tp: 'st', p: 1, pu: 'st', t: 3},
            {id: 'c6', tp: 'sum'}
          ];
          res.sections = [];
          res.global = {
            visibleKpis: [],
            contextDate: moment().format('YYYY-MM-DD'),
            farm: [-1],
            breed: [],
            useCommonWeights: false
          };
          if ($state.current.data && ng.isFunction($state.current.data.viewResetCallback)) {
            $state.current.data.viewResetCallback(res);
          }
          console.log('resetting settings', res);
          return res;
        }
      }
    ])
    .factory('CustomReportSetuperColumns', [function () {
      var c = [
        {
          cannotTriggerInsert: true,
          cssClass: 'cf-text-align-center',
          editor: Slick.Editors.Cloudfarms.warningEditor,
          field: '$$$error',
          formatter: Slick.Formatters.Cloudfarms.error,
          id: '$$$error',
          mandatory: true,
          name: M('<i class=\"awesome-icon-info heading-centered-icon\"><\/i>'),
          resizable: false,
          sortable: true,
          toolTip: M('js.label.info'),
          width: 16
        },
        {
          cannotTriggerInsert: false,
          editor: null,
          field: 'code',
          id: 'code',
          name: M('js.label.kpi'),
          sortable: false,
          toolTip: M('js.label.kpi'),
          width: 250
        },
        {
          cannotTriggerInsert: true,
          cssClass: 'cf-text-align-right',
          editor: Slick.Editors.Cloudfarms.decimal({decimalPlaces: 0}),
          formatter: Slick.Formatters.Cloudfarms.decimal(0),
          field: 'days',
          id: 'days',
          name: M('js.label.days'),
          sortable: false,
          toolTip: M('js.label.days'),
          width: 60
        },
        {
          cannotTriggerInsert: true,
          editor: null,
          field: 'customCode',
          id: 'customCode',
          name: M('js.label.sowcard.section.custom'),
          sortable: false,
          toolTip: M('js.label.sowcard.section.custom'),
          width: 100
        }
      ];
      c.defaultColumns = ng.copy(c);

      return c;
    }
    ])
    .factory('KpiDefinitionsEditorFormatter', ['$http', function ($http) {
      var hashAllByCode = {}, serverDataAll = [], isLoaded = false;

      function autocompleteFormatter(item) {
        if (!item) return '';
        return ng.isObject(item) ? item.nameExt : item;
      }

      $http.get('/api/reports/kpidefinitions/all/list').success(function (response) {
        isLoaded = true;
        serverDataAll = response;
        hashAllByCode = CF.createHashObject(response, 'code');
      }).error(function (response) {
        console.log('failed to log kpidefinitions - reponse: ' + response);
      });
      var dataSource = {
        getLength: function () {
          return serverDataAll.length;

        },
        getItem: function (i) {
          return serverDataAll[i];
        }
      };

      return {
        isLoaded: function () {
          return isLoaded;
        },
        byCode: function (code) {
          return hashAllByCode[code];
        },
        gridEditor: Slick.Editors.Cloudfarms.AutoComplete(dataSource, false, 'code', false, true, autocompleteFormatter),
        formatter: function (row, cell, value, column, item) {
          return '<a target="help" href="https://help.cloudfarms.com/kpis#' + value + '">' + (value && hashAllByCode[value] && hashAllByCode[value].nameExt ? hashAllByCode[value].nameExt : (value && hashAllByCode[value] && hashAllByCode[value].name ? hashAllByCode[value].name : value)) + '</a>';
        },
        formatter2: function (row, cell, value) {
          if (typeof value === 'string') {
            return M('js.report.' + value.toLowerCase(), M('js.label.energy.unit.' + FS.energy), M('js.label.weight.unit.' + FS.weight), FS.currency);
          } else {
            return value.toLowerCase();
          }
        }
      };
    }])
    .factory('FarmsOptions', ['$http', function ($http) {
      var serverDataAll = [], isLoaded = false;
      $http.get('/api/reports/farms2').success(function (response) {
        isLoaded = true;
        ng.extend(serverDataAll, response);
      }).error(function (response) {
        console.error('loading farms list failed :' + response);
      });

      return {
        isLoaded: function () {
          return isLoaded;
        },
        getFarms: function () {
          return serverDataAll;
        },
        defaultFarms: [-1]
      };

    }])
    .factory('CustomReportSetuperData', ['$http', function ($http) {
      return CustomReportSetuperData;

      function CustomReportSetuperData(data, options, columns) {
        var dataVersion = 0;
        var self = this;
        self.rawData = data;
        this.options = options;
        this.columns = columns;
        this.invalidator = {};
        this.activeCell = {cell: 1, row: 0};
        this.validationStyles = {};

        this.data = {
          getLength: function () {
            return self.rawData.length;
          },
          getItem: function (i) {
            return self.rawData[i];
          },
          addItem: function (item) {
            item.id = CF.maxInArray(data, 'id') + 1;
            self.rawData.push(item);
            dataVersion++;
            return item;
          },
          removeItem: function (i) {
            self.rawData.splice(i, 1);
            dataVersion++;
          },
          getItemMetadata: function (i) {

          }
        };

        this.getDataVersion = function () {
          return dataVersion;
        };
        this.rowById = function (id) {
          for (var i = 0; i < self.rawData.length; i++)
            if (id === self.rawData[i].id)
              return i;
        };

        this.removeCurrentItem = function () {
          self.rawData.splice(self.activeCell.row, 1);
          dataVersion++;
        };
        this.invalidateAll = function () {
          for (var i = 0; i < self.rawData.length; i++)
            self.invalidator.invalidateRow(i);
        };

        this.onCellChange = function (row, cell, item) {
          dataVersion++;
          self.validateItem(item);

          self.invalidator.invalidateRow(row);
          CF.updateCellsCSS(item, self.validationStyles, row, self.columns);
        };

        this.invalidateItem = function (item, id, row) {
          row = row ? row : self.rowById(item ? item.id : id);
          self.invalidator.invalidateRow(row);
        };
        this.validateItem = function (item) {
          delete item.$$$error;
          if (CF.isEmpty(item.code))
            CF.addError(item, 'code', M('validation.error.prop.notempty.field', M('js.label.kpi')));
          return item.$$$error;
        };
        this.onAddNewRow = function (args) {
          var item = self.data.addItem(args.item);
          var row = self.rowById(item.id);
          self.invalidator.invalidateRow(row);
          CF.updateCellsCSS(item, self.validationStyles, row, self.columns);
        };
        this.updateColors = function (item, row) {
          if (item) {
            row = row ? row : self.rowById(item.id);
            CF.updateCellsCSS(item, self.validationStyles, row, self.columns);
          }
        };
        this.getCurrentItem = function () {
          return self.rawData[self.activeCell.row];
        };
      }
    }])

    .factory('CustomReportColumns', ['KpiDefinitionsEditorFormatter', function (KpiDefinitions) {
      var c = {
        lineId: {
          //  headerCssClass:'no-height',
          cannotTriggerInsert: false,
          editor: null,
          field: 'lineId',
          formatter: KpiDefinitions.formatter,
          id: 'lineId',
          name: M('js.report.lineid'),
          shortName: M('js.report.lineid'),
          longName: M('js.report.lineid') + '<br> &nbsp;',
          sortable: false,
          toolTip: M('js.report.lineid'),
          width: 230,
          hideFilter: true
        },
        number: {
          //   headerCssClass:'no-height',
          cannotTriggerInsert: false,
          cssClass: 'cf-text-align-right',
          editor: null,
          field: '',
          formatter: Slick.Formatters.Cloudfarms.flexDecimal('decimalPlaces'),
          id: '',
          name: '',
          sortable: false,
          toolTip: '',
          width: 190
        },
        add: {
          // headerCssClass:'no-height',
          cssClass: 'cf-text-align-center',
          field: '$$$add',
          id: '$$$add',
          name: M('js.label.add.column'),
          resizable: false,
          sortable: false,
          toolTip: M('js.label.add'),
          width: 80
        },
        empty: {
          cssClass: 'cf-text-align-center',
          field: '$$$empty',
          id: '$$$empty',
          name: '&nbsp;',
          resizable: false,
          sortable: false,
          toolTip: '',
          width: 80,
          hideFilter: true
        }
      };
      var fixedStart = [ng.copy(c.lineId)], fixedEnd = [ng.copy(c.add), ng.copy(c.empty)];

      return {
        c: c,
        fixedStart: fixedStart,
        fixedEnd: fixedEnd
      };
    }])
    .factory('CustomReportPeriodDefinition', ['settings', 'StocktakingDates', 'CustomReportPeriodText', function (settings, StocktakingDates, CRPeriodText) {
      var DF_ISO = 'YYYY-MM-DD', DF_FARM_MOMENT = CF.dateTime.dateFormatToMomentFormat(settings.getDateFormat()), required = getRequired();

      return {
        compareDefinitionsByRequired: CF.compareObjectByPropertiesFactory(getRequired()),
        required: getRequired(),
        names: getAllNames(),
        getDefinitionOptions: getDefinitionOptions,
        definitionOptions: getDefinitionOptions(),
        headerNameBytType: getHeaderNameByType(),
        isColDefValid: isColDefValid,
        periodDiff: periodDiff,
        periodFromColDef: periodFromColDef,
        colDefFromDates: colDefFromDates,
        sumPeriodFromFilters: sumPeriodFromFilters,
        prepareParams: prepareParams,
        paramsFromColDef: paramsFromColDef,
        addDefaultDefinition: addDefaultDefinition,
        filterMalformed: filterMalformed,
        getDefinitionOptionsFromPeriod: getDefinitionOptionsFromPeriod,
        getDefOptionByTypeByPeriodUnit: getDefOptionByTypeByPeriodUnit,
        columnTypeFromDefType: function (tp) {
          return ['rp', 'st', 'sp'].indexOf(tp) > -1 ? 'p' : tp;
        }
      };

      function filterMalformed(colsDefs) {
        var res = [];
        if (!ng.isArray(colsDefs))
          throw new Error('column definitions is not array');

        for (var i = 0; i < colsDefs.length; i++) {
          var def = colsDefs[i];
          if (!ng.isObject(def) || ng.isArray(def)) {
            continue;
          }
          try {
            CF.checkRequiredProperties(def, ['id', 'tp']);
          } catch (err) {
            continue;
          }
          res.push(def);
        }
        return res;
      }

      function addDefaultDefinition(colsDefs, gridColumns) {
        var addPos = CF.findInArrayIndex(gridColumns, 'id', '$$$add');
        colsDefs.splice(Math.max(addPos - 1, 0), 0, {
          id: 'c' + colsDefs.length + 1,
          tp: 'rp',
          pu: 'w',
          p: 1,
          t: 0,
          f: 1
        });
        console.log('addDefaultDefinition');
        return colsDefs;
      }


      function getDefOptionByTypeByPeriodUnit(defOptions, type, periodUnit) {
        for (var i = 0; i < defOptions.length; i++) {
          var defOpt = defOptions[i];
          if (defOpt.tp === type && ( !defOpt.pu || defOpt.pu === periodUnit))
            return defOpt;
        }
        console.error(defOptions, type, periodUnit);
        throw new Error('definition not found');
      }

      function getDefinitionOptionsFromPeriod(periodStart, periodEnd, colsDefs, type, periodUnit, id, contextDate) {
        var options = [];
        if (!CF.isEmpty(periodStart) && !CF.isEmpty(periodEnd) && moment(periodEnd).isAfter(moment(periodStart), 'day')) {
          options = getDefinitionOptions();
          ng.forEach(options, function (defOpt, key) {//FIXME: try to use thow catch to will with marginal cases on loaded stocktaking,
            defOpt.def = colDefFromDates(periodStart, periodEnd, defOpt.tp, defOpt.pu, id, contextDate);
            defOpt.period = periodFromColDef(defOpt.def, colsDefs, contextDate);
            defOpt.rank = periodDiff(periodStart, periodEnd, defOpt.period.from, defOpt.period.to);

            defOpt.periodStr = CRPeriodText.periodDatesString(defOpt.period.from, defOpt.period.to, DF_FARM_MOMENT);
            defOpt.daysStr = CRPeriodText.periodDaysString(defOpt.period.from, defOpt.period.to, DF_FARM_MOMENT);
            defOpt.periodLengthTranslated = CRPeriodText.periodLengthTranslated(defOpt.def);
            defOpt.periodStartTranslated = CRPeriodText.periodStartTranslated(defOpt.def);
          });
        }
        return options;
      }


      function getDefinitionOptions() {
        return [
          {name: M('js.label.staticperiod'), tp: 'sp', periodStr: '', daysStr: ''},
          {
            name: M('js.label.relativeperiod') + ' ' + M('js.label.month'),
            tp: 'rp',
            pu: 'M',
            periodStr: '&nbsp;',
            daysStr: '&nbsp;'
          },
          {
            name: M('js.label.relativeperiod') + ' ' + M('js.label.week'),
            tp: 'rp',
            pu: 'w',
            periodStr: '&nbsp;',
            daysStr: '&nbsp;'
          },
          {
            name: M('js.label.relativeperiod') + ' ' + M('js.label.quarter'),
            tp: 'rp',
            pu: 'Q',
            periodStr: '&nbsp;',
            daysStr: '&nbsp;'
          },
          {name: M('js.title.stocktaking'), tp: 'st', pu: 'st', periodStr: '&nbsp;', daysStr: '&nbsp;'}
        ];
      }

      function getAllNames() {
        return [
          {
            abbr: 'tp',
            name: 'columnType',
            prefix: 'js.report.',
            options: [
              {id: 'rp', name: 'relativePeriod', pu: {}},
              {id: 'sp', name: 'staticPeriod'},
              {id: 'st', name: 'stocktaking'},
              {id: 'dif', name: 'diff'},
              {id: 'kg', name: 'kpiGoal'},
              {id: 'sum', name: 'sum'}
            ]
          },
          {abbr: 'id', name: 'columnId'},
          {abbr: 'pu', prefix: 'js.report.', name: 'periodUnit'},
          {abbr: 'su', name: 'stepUnit'},
          {abbr: 't', name: 'periodEnd'},
          {abbr: 'f', name: 'periodStart'},
          {abbr: 'p', prefix: 'js.report.', name: 'periodLength'}
        ];
      }

      function getRequired() {
        return ['id', 'tp', 'pu', 'p', 't', 'f'];
      }

      function getHeaderNameByType() {
        return {
          'sp': M('js.report.period'),
          'rp': M('js.report.period'),
          'st': M('js.report.period'),
          'sum': M('js.report.periodwhole'),
          'dif': M('js.report.4diff'),
          'kg': M('js.report.kpigoal')
        };
      }

      /**
       * @param colDefs {Array} grid columns with columns definitions (def:{}}
       * @param filters
       * @param contextDate {String} reference date in ISO format for calculating stocktaking and relative periods
       * @returns {Array} list of periods in format accepted by server, exlcuding invalid column definitions
       */
      function prepareParams(colDefs, contextDate) {
        var params = [], i, def;
        var correctColDefs = filterMalformed(colDefs);

        for (i = 0; i < correctColDefs.length; i++) {
          def = correctColDefs[i];
          if (isColDefValid(def, correctColDefs, contextDate)) {
            params.push(paramsFromColDef(periodFromColDef(def, correctColDefs, contextDate), def));
          }
        }
        return params;
      }

      /**
       *
       * @param period {Object} with from, to properties
       * @param colDef {Object} with id,tp - type
       * @returns {Object} params in format accepted by server
       */
      function paramsFromColDef(period, colDef) {
        if (ng.isUndefined(colDef.tp)) {
          console.error('column definition missing "tp" ( column type)');
        }
        switch (colDef.tp) {
          case 'kg':
            return {id: colDef.id, tp: colDef.tp};
          case 'sum':
            return {id: colDef.id, tp: colDef.tp, to: period.to, from: period.from};//from,to used only for display purpose
          case 'dif':
            return {
              id: colDef.id,
              tp: colDef.tp,
              refid: colDef.refid,
              to: period.to,
              from: period.from
            };
          default:
            return {id: colDef.id, from: period.from, to: period.to, tp: 'sp'};//all the others(st,rt,sp) are converted to standard periods, because we calculate from, to for them
        }
      }

      /**
       * Calculates period in from column definition
       *  Type(tp) dependencies : {sp: ['f', 't'], rp: {M: ['f', 'p'], w: ['f', 'p']}, st: ['p', 't'], dif: ['refid']}
       * @param def column definition
       * @param colsDefs
       * @param contextDate {String}  ISO date string
       * @returns {{from: String, to: String}}
       */
      function periodFromColDef(def, colsDefs, contextDate) {
        var from, to, i;
        switch (def.tp) {
          case 'sp' :
            from = def.f;
            to = def.t;
            break;
          case 'rp' :
            if (['M', 'Q', 'w'].indexOf(def.pu) < 0)
              throw Error('wrong period unit');
            to = moment(contextDate, DF_ISO).subtract(parseInt(def.t), def.pu).endOf(def.pu).format(DF_ISO);
            from = moment(to).subtract(parseInt(def.p) - 1, def.pu).startOf(def.pu).format(DF_ISO);
            break;
          case 'st' :
            var offSet;
            for (i = 0; i < StocktakingDates.getLoadedLength(); i++) {
              if (!moment(contextDate, DF_ISO).isBefore(moment(StocktakingDates.getItem(i), DF_ISO), 'days')) {
                offSet = i;
                break;
              }
            }
            from = StocktakingDates.getItem(offSet + parseInt(def.t) + parseInt(def.p));
            from = moment(from).add(1, 'days').format(DF_ISO);
            to = StocktakingDates.getItem(offSet + parseInt(def.t));
            break;
          case 'dif':
            if (colsDefs) {
              var refDef = CF.findInArray(colsDefs, 'id', def.refid);
              return periodFromColDef(refDef, null, contextDate);
            }
            break;
          default:
            from = moment().format(DF_ISO);
            to = moment().format(DF_ISO);
        }
        return {from: from, to: to};
      }

      /**
       *
       *
       * @param from ISO date text or moment
       * @param to  ISO date text or moment
       * @param type [['rp','relativePeriod'],['st','stocktaking'],['sp',staticPeriod']]
       * @param periodUnit [ ['M','month],['w','week'],['Q,'quarter']]
       * @returns {{}} column definition for chosen type and periodUnit : {sp: ['f', 't'], rp: {M: ['f', 'p'], w: ['f', 'p']}, st: ['p', 't'], diff: ['refid']}
       * @param id
       */

      function colDefFromDates(from, to, type, periodUnit, id, contextDate) {
        var i = 0;
        var def = {tp: type, pu: periodUnit, id: id};
        switch (type) {
          case 'sp' :
            def.f = from;
            def.t = to;
            def.pu = 'sp';
            break;
          case 'rp' :
            var contextMoment = contextDate && moment(contextDate).isValid() ? moment(contextDate) : moment();
            def.t = contextMoment.endOf(periodUnit).diff(moment(to, DF_ISO).endOf(periodUnit), periodUnit);
            def.p = moment(to, DF_ISO).startOf(periodUnit).diff(moment(from, DF_ISO).startOf(periodUnit), periodUnit) + 1;
            def.f = def.t + def.p;
            break;
          case 'st' :
            for (i = 0; i < StocktakingDates.getLoadedLength(); i++) {
              if (moment(to, DF_ISO).isAfter(moment(StocktakingDates.getItem(i), DF_ISO), 'day')) {
                def.t = i - 1 > 0 ? i - 1 : 0;
              }
              if (ng.isDefined(def.t) && moment(from, DF_ISO).isAfter(moment(StocktakingDates.getItem(i), DF_ISO), 'day')) {
                def.p = i - def.t;
                break;
              }
            }
            def.p = def.p || 1;
            def.t = def.t || 0;
            def.f = def.t + def.p;
            def.pu = 'st';
            break;
          default:
            throw Error('calculateColsDefsFromDates - wrong type');
        }

        return def;
      }

      /**
       * Assumes filter contains values with from,to calculated
       * @param filters
       * @returns {{from: (*|{icon, title, actions}|string|{}), to: (*|{icon, title, actions}|string|{})}}
       */
      function sumPeriodFromFilters(filters, ignoreColumnDef) {
        var i, minFrom, maxTo;
        var keys = Object.keys(filters);
        for (i = 0; i < keys.length; i++) {
          var f = filters[keys[i]];
          if (ng.isObject(f.value) && f.value.start && f.value.end && (!ignoreColumnDef || keys[i] != ignoreColumnDef.id)) {
            if (ng.isUndefined(minFrom)) {
              minFrom = moment(f.value.start);
              maxTo = moment(f.value.end);
            } else {
              minFrom = moment(f.value.start).isBefore(minFrom, 'day') ? moment(f.value.start) : minFrom;
              maxTo = moment(f.value.end).isAfter(maxTo, 'day') ? moment(f.value.end) : maxTo;
            }
          }
        }
        return {
          from: moment(minFrom).format(DF_ISO),
          to: moment(maxTo).format(DF_ISO)
        };
      }

      function isColDefValid(def, allDefinitions, contextDate) {
        if (CF.isEmpty(def.tp) || CF.isEmpty(def.id))
          return false;
        switch (def.tp) {
          case 'sp':
            return !CF.isEmpty(def.f) && !CF.isEmpty(def.t) && moment(def.t).diff(moment(def.f), 'day') > 0;
          case 'rp':
            return !CF.isEmpty(def.pu) && !CF.isEmpty(def.p) && !CF.isEmpty(def.t) && parseInt(def.t) >= 0 && parseInt(def.p) > 0;
          case 'st':
            var i, offSet;
            if (CF.isEmpty(def.p) || CF.isEmpty(def.t) || parseInt(def.p) < 1 || parseInt(def.t) < 0) {
              console.error('isColDefValid', def.tp, def);
              return false;
            }
            if (StocktakingDates.isLoading()) {
              console.error('isColDefValid -loading stocktakingdates', def.tp, def);
              return false;
            }

            for (i = 0; i < StocktakingDates.getLoadedLength(); i++) {
              StocktakingDates.getItem(i);
              if (!moment(contextDate, DF_ISO).isBefore(moment(StocktakingDates.getItem(i), DF_ISO), 'day')) {
                offSet = i;
                break;
              }
            }
            return !(ng.isUndefined(offSet) || !StocktakingDates.getItem(offSet + parseInt(def.t)) || !StocktakingDates.getItem(offSet + parseInt(def.t) + parseInt(def.p)));
          case 'sum':
            return true;
          case 'kg':
            return true;
          case 'dif':
            if (CF.isEmpty(def.refid))
              return false;
            return !!CF.findInArrayByNestedProp(allDefinitions, 'def', 'id', def.refid);
        }

      }

      function periodDiff(from1, to1, from2, to2) {
        return Math.abs(moment(from1, DF_ISO).diff(moment(from2, DF_ISO), 'days')) + Math.abs(moment(to1).diff(moment(to2, DF_ISO), 'days'));
      }

    }])

    .factory('CustomReportPeriodText', ['$filter', 'settings', function ($filter, settings) {
      var DF_ISO = 'YYYY-MM-DD', DF_FARM_MOMENT = CF.dateTime.dateFormatToMomentFormat(settings.getDateFormat()), messagesMapper = getMessageMapper();

      return {
        messagesMapper: messagesMapper,
        periodLengthTranslated: periodLengthTranslated,
        periodStartTranslated: periodStartTranslated,
        periodDatesString: periodDatesString,
        periodDaysString: periodDaysString
      };
      function getMessageMapper() {
        return {
          periodLength: 'js.label.period.length',
          periodStart: 'js.label.period.start',
          base: 'js.timeunit',
          post: 'lowercase',
          plural: 'plural',
          st: {st: 'stocktaking'},
          rp: {Q: 'quarter', M: 'month', 'd': 'day', w: 'week'}
        };
      }

      function periodLengthTranslated(def) {
        if (['sp', 'sum', 'dif', 'kg'].indexOf(def.tp) > -1)
          return '';
        return M(messagesMapper.periodLength, def.p, M(messagesMapper.base + '.' + messagesMapper[def.tp][def.pu || def.tp] + '.' + messagesMapper.post + (def.p > 1 ? '.' + messagesMapper.plural : '')));
      }

      function periodStartTranslated(def) {
        if (['sp', 'sum', 'dif', 'kg'].indexOf(def.tp) > -1)
          return '';
        return M(messagesMapper.periodStart, def.t, M(messagesMapper.base + '.' + messagesMapper[def.tp][def.pu || def.tp] + '.' + messagesMapper.post + ( def.t + def.p + 1 > 1 ? '.' + messagesMapper.plural : '')));

      }

      function periodDatesString(from, to, df) {
        return moment(from, DF_ISO).format(df) + '..' + moment(to, DF_ISO).format(df);
      }

      function periodDaysString(from, to, df) {
        return (moment(to, DF_ISO).diff(moment(from, DF_ISO), 'days') + 1) + ' ' + M('js.label.days');
      }


    }])

    .factory('CustomReportColumnsUtils', ['$filter', 'StocktakingDates', 'settings', 'CustomReportColumns', 'CustomReportPeriodDefinition', 'CustomReportPeriodText', function ($filter, StocktakingDates, settings, CustomReportColumns, CRPeriodDefinition, CRPeriodText) {
      var DF_ISO = 'YYYY-MM-DD', DF_FARM_MOMENT = CF.dateTime.dateFormatToMomentFormat(settings.getDateFormat());
      return {
        createColumnsFromSettings: createColumnsFromSettings,
        createGridColumns: createGridColumns,
        createFilters: createFilters,
        createGridColumnsAndFilters: createGridColumnsAndFilters,
        updateFilters: updateFilters,
        updateFilter: updateFilter,
        syncColsDefsFromGridColumns: syncColsDefsFromGridColumns,
        syncFromSettings: syncFromSettings,
        defsFromFilters: defsFromFilters,
        getTypeOptionsFromColsDefs: getTypeOptionsFromColsDefs,
        getColumnIdsOptionsFromFilters: getColumnIdsOptionsFromFilters
      };


      function getColumnIdsOptionsFromFilters(filters, colsDefs) {
        var res = {};
        var keys = Object.keys(filters);
        for (var i = 0; i < colsDefs.length; i++) {
          var def = colsDefs[i];
          if (!def || !def.tp)//some filters dont have definitions (e.g $$$add )
            continue;
          if ((['rp', 'st', 'sp'].indexOf(def.tp) > -1) && filters.isValid && filters[def.id].value && filters[def.id].value.start && filters[def.id].value.end) {
            var dates = CRPeriodText.periodDatesString(filters[def.id].value.start, filters[def.id].value.end, DF_FARM_MOMENT);
            var days = CRPeriodText.periodDaysString(filters[def.id].value.start, filters[def.id].value.end, DF_FARM_MOMENT);
            var text = dates + '  (' + days + ')';
            res[def.id] = {name: text};
          }
        }
      }

      function getTypeOptionsFromColsDefs(tp, colsDefs) {
        var res = {
          p: {name: M('js.label.period')},
          kg: {name: M('js.title.kpigoal')},
          //     dif: {name: M('js.report.4diff')},
          sum: {name: M('js.report.sum')}
        };
        for (var i = 0; i < colsDefs.length; i++) {
          var def = colsDefs[i];
          if (tp !== 'kg' && def.tp === 'kg')
            delete res.kg;
          if (tp !== 'sum' && def.tp === 'sum')
            delete res.sum;
          if (tp !== 'dif' && def.tp === 'dif')
            delete res.dif;
        }
        return res;
      }

      function syncColsDefsFromGridColumns(colDefs, filters, gridColumns) {
        if (hasToCreateAll(colDefs, gridColumns)) {
          return defsFromFilters(filters, gridColumns);
        }
        return colDefs;
      }

      function syncFromSettings(gridColumns, colDefs, filters, global) {
        if (ng.isUndefined(gridColumns) || hasToCreateAll(colDefs, gridColumns)) {
          return createColumnsFromSettings(colDefs, global);
        } else {
          updateFilters(filters, colDefs, global);
        }
        return {
          gridColumns: gridColumns,
          filters: filters
        };
      }

      function hasToCreateAll(colDefs, gridColumns) {
        var colsWithDef = [], i;
        for (i = 0; i < gridColumns.length; i++) {
          if (gridColumns[i].hasDefinition)
            colsWithDef.push(gridColumns[i]);
        }

        for (i = 0; i < Math.max(colsWithDef.length, colDefs.length); i++) {
          if (!colDefs[i] || !colsWithDef[i] || colsWithDef[i].id !== colDefs[i].id) {
            return true;
          }
        }
      }


      function hasToUpdateAll(colDefs, filters) {
        for (var i = 0; i < colDefs.length; i++) {
          hasToUpdate(colDefs[i], filters);
        }
      }

      function hasToUpdate(colDef, filters) {

        if (!filters[colDef.id] || !filters[colDef.id].def)
          return true;
        if (!CRPeriodDefinition.compareDefinitionsByRequired(filters[colDef.id].def, colDef))
          return true;
      }

      function defsFromFilters(filters, gridColumns) {
        var colsWithDef = [], res = [], i;
        for (i = 0; i < gridColumns.length; i++) {
          if (gridColumns[i].hasDefinition)
            colsWithDef.push(gridColumns[i]);
        }

        for (i = 0; i < colsWithDef.length; i++) {
          var col = colsWithDef[i], f = filters[col.id];
          if (ng.isObject(f.def)) {
            res.push(f.def);
          }
        }
        return res;
      }

      /**
       * Create columns and filters from settings. Should be called after view change, column reordering, removal.
       * @param colsDefs
       * @param global
       * @returns {{gridColumns, filters}|*}
       */
      function createColumnsFromSettings(colsDefs, global) {
        var res = createGridColumnsAndFilters(colsDefs, ng.copy(CustomReportColumns.fixedStart), ng.copy(CustomReportColumns.fixedEnd));
        updateFilters(res.filters, colsDefs, global);
        return res;
      }

      /**
       * Creates slick grid columns from columns definitions.
       * @param colsDefs {Array} array of objects with : id,tp - type,f- from,t- to,p - period, pu -period unit
       * @returns {*[]} slick grid columns
       * @param fixedStartColumn
       * @param fixedEndColumns
       */
      function createGridColumns(colsDefs, fixedStartColumn, fixedEndColumns) {
        var gridColumns = [], i;
        Array.prototype.push.apply(gridColumns, fixedStartColumn);
        //create columns and add them column definitions
        for (i = 0; i < colsDefs.length; i++) {
          var numCol = ng.copy(CustomReportColumns.c.number);
          var def = colsDefs[i];
          numCol.id = numCol.field = def.id;
          numCol.name = numCol.toolTip = CRPeriodDefinition.headerNameBytType[def.tp];
          numCol.tp = def.tp;
          numCol.hasDefinition = true;
          switch (def.tp) {
            case 'kg':
              numCol.width = 120;
              break;
          }
          gridColumns.push(numCol);
        }
        Array.prototype.push.apply(gridColumns, fixedEndColumns);
        return gridColumns;
      }

      function createFilters(gridColumns) {
        var filters = {}, i;
        for (i = 0; i < gridColumns.length; i++) {
          var col = gridColumns[i];
          if (col.hideFilter)
            continue;
          if (col.id === '$$$add') {
            filters[col.id] = {type: Slick.Filters.Cloudfarms.CfAddColumnDefinitionFilter};
            continue;
          }
          filters[col.id] = {
            type: Slick.Filters.Cloudfarms.ColumnPeriodSetup,
            value: {start: '', end: '', text: '', p: ''}
          };


          if (!ng.isObject(filters[col.id]) || ng.isUndefined(filters[col.id].tp)) {
            if (['dif', 'kg'].indexOf(col.tp) > -1)
              filters[col.id].inputCss = {'background-color': 'transparent', 'border': 'none', 'color': 'transparent'};
            else if ('sum' === col.tp) {
              filters[col.id].inputCss = {'background-color': '#ddd'};
            } else {
              filters[col.id].inputCss = {};
            }
          }
        }
        return filters;
      }


      function createGridColumnsAndFilters(colsDefs, fixedStartColumn, fixedEndColumns) {
        CF.checkChildrenForRequiredProperties(colsDefs, CRPeriodDefinition.required);
        var gridColumns = createGridColumns(colsDefs, fixedStartColumn, fixedEndColumns);
        var filters = createFilters(gridColumns);
        return {
          gridColumns: gridColumns,
          filters: filters
        };
      }


      /**
       * Update all filters.
       * Assumes that filters are already created by createFilters and have value, type
       * @param filters
       * @param colDefs
       * @param global
       * @returns {*}
       */
      function updateFilters(filters, colDefs, global) {
        if (!global.contextDate) {
          console.error('global: contextDate missing', global);
        }
        var i, def;

        //period filters
        for (i = 0; i < colDefs.length; i++) {
          def = colDefs[i];
          if (['sum', 'kg', 'dif'].indexOf(def.tp) > -1)
            continue;
          updateFilter(filters[def.id], colDefs, i, global.contextDate);
        }
        //aggregating filters
        for (i = 0; i < colDefs.length; i++) {
          def = colDefs[i];
          switch (def.tp) {
            case 'sum':
              updateSumFilter(filters[def.id], filters, colDefs, i, global.contextDate);
              break;
            case 'kg':
              updateKpiGoalFilter(filters[def.id], filters, colDefs, i, global.contextDate);
              break;
            case 'dif':
              updateDifFilter(filters[def.id], filters, colDefs, i, global.contextDate);
              break;
          }
        }
        return filters;
      }

      /**
       * Update filter - text, headers, from,to.
       * Assumes filters contain value,type
       * @param filter
       * @param colDefs
       * @param pos
       * @param contextDate
       */
      function updateFilter(filter, colDefs, pos, contextDate, excludeValue) {
        var period, periodStr;
        var def = colDefs[pos];
        if (def && CRPeriodDefinition.isColDefValid(def, colDefs, contextDate)) {
          period = CRPeriodDefinition.periodFromColDef(def, colDefs, contextDate);
          periodStr = CRPeriodText.periodDatesString(period.from, period.to, DF_FARM_MOMENT);
          filter.daysStr = CRPeriodText.periodDaysString(period.from, period.to, DF_FARM_MOMENT);
          filter.periodLengthTranslated = CRPeriodText.periodLengthTranslated(def) || '&nbsp;';
          filter.periodStartTranslated = CRPeriodText.periodStartTranslated(def) || '&nbsp;';
          filter.isValid = true;
          if (!excludeValue && (filter.value.start !== period.from || filter.value.end !== period.to))
            filter.value = {text: periodStr, start: period.from, end: period.to};
        } else {
          filter.isValid = false;
          filter.daysStr = M('js.invalid');
          filter.periodStartTranslated = '&nbsp;';
          filter.periodLengthTranslated = '&nbsp;';
        }
        filter.def = def;
      }

      /**
       * Assumes that all other filter have updated value.start,value.end values.
       * @param filter
       * @param filters
       * @param colDefs
       * @param pos
       * @param contextDate
       */
      function updateSumFilter(filter, filters, colDefs, pos, contextDate) {
        var period, periodStr;
        var def = colDefs[pos];
        if (CRPeriodDefinition.isColDefValid(def, colDefs, contextDate)) {
          period = CRPeriodDefinition.sumPeriodFromFilters(filters, colDefs[pos]);
          periodStr = CRPeriodText.periodDatesString(period.from, period.to, DF_FARM_MOMENT);
          filter.daysStr = CRPeriodText.periodDaysString(period.from, period.to, DF_FARM_MOMENT);
          filter.periodStartTranslated = '&nbsp;';
          filter.periodLengthTranslated = '&nbsp;';
          filter.isValid = true;
          if (filter.value.start !== period.from || filter.value.end !== period.to)
            filter.value = {text: periodStr, start: period.from, end: period.to};
        } else {
          filter.isValid = false;
          filter.daysStr = filter.periodStartTranslated = filter.periodLengthTranslated = '&nbsp;';
        }
        filter.def = def;
      }

      function updateDifFilter(filter, filters, colDefs, pos, contextDate) {
        var def = colDefs[pos];
        if (CRPeriodDefinition.isColDefValid(def, colDefs, contextDate)) {
          filter.isValid = true;
          filter.daysStr = '&nbsp;';
          filter.periodStartTranslated = '&nbsp;';
          filter.periodLengthTranslated = '&nbsp;';
        } else {
          filter.isValid = false;
          filter.daysStr = filter.periodStartTranslated = filter.periodLengthTranslated = '&nbsp;';
        }
        filter.def = def;
      }

      function updateKpiGoalFilter(filter, filters, colDefs, pos, contextDate) {
        var def = colDefs[pos];
        if (CRPeriodDefinition.isColDefValid(def, colDefs, contextDate)) {
          filter.daysStr = '&nbsp;';
          filter.periodStartTranslated = '&nbsp;';
          filter.periodLengthTranslated = '&nbsp;';
          filter.isValid = true;
        } else {
          filter.isValid = false;
          filter.daysStr = filter.periodStartTranslated = filter.periodLengthTranslated = '&nbsp;';
        }
        filter.def = def;
      }
    }

    ])


    .factory('ReportTypeSettings', ['settings', 'KpiDefinitionsEditorFormatter', 'ReportTypeSettingColumns',
      function (settings, KpiDefinitionsEditorFormatter, ReportTypeSettingColumns) {
        var reportTypes = [
            {name: M('js.report.week.title'), db: 'report.production'},
            {name: M('js.label.breedinganimals'), db: 'report.breed_efficiency'},
            {name: M('js.label.inhab.plural.gilt'), db: 'report.gilt_efficiency'},
            {name: M('js.label.inhab.plural.wean'), db: 'report.weaner_efficiency'},
            {name: M('js.label.inhab.plural.fatt'), db: 'report.fattener_efficiency'},
            {name: M('js.label.location'), db: 'report.location_efficiency'}
          ],
          _hiddenPostfix = '_h';
        var i = 0;

        return {
          getReportTypes: function () {
            return reportTypes;
          },
          ReportTypeSettingsData: ReportTypeSettingsData,
          getReportTypesData: getReportTypesData,
          registerCodeFormatterWatch: registerCodeFormatterWatch,
          ReportTypeSettingColumns: ReportTypeSettingColumns
        };

        //factory
        function ReportTypeSettingsData(columns, name, dbName, dbPostfix, scope, saveDestinationName) {
          var self = this, data = [];
          this.dataVersion = 0;
          this.name = name;
          this.dbName = dbName;
          this.dbPostfix = dbPostfix;
          this.activeCell = {row: 0, cell: 0};
          this.invalidator = {};
          this.columns = columns;
          this.options = {
            editable: true,
            enableAddRow: false,
            enableCellNavigation: true,
            enableColumnReorder: true,
            showHeaderRow: false,
            headerRowHeight: 30,
            rowHeight: 18,
            fullWidthRows: true,
            multiSelect: false,
            autoEdit: true,
            autoHeight: true,
            syncColumnCellResize: true,
            asyncEditorLoading: true
          };
          this.data = {
            getDataVersion: function () {
              return self.dataVersion;
            },
            getLength: function () {
              return data.length;
            },
            getItem: function (i) {
              return data[i];
            },
            addItem: function (item) {
              data.push(item);
            }
          };
          this.initializeHidden = function (isAdmin) {
            self.options.editable = isAdmin;
            data = [];

            var allKpis = settings.getOrg(self.dbName);
            allKpis = allKpis ? allKpis : [];

            var hidden = settings.getOrg(self.dbName + self.dbPostfix);
            hidden = hidden ? hidden : [];

            for (var i = 0; i < allKpis.length; i++) {
              if (ng.isArray(allKpis[i].kpis)) {
                for (var j = 0; j < allKpis[i].kpis.length; j++) {
                  var kpi = allKpis[i].kpis[j];
                  var item = {code: kpi.code};
                  item.show = hidden.indexOf(kpi.code) < 0;
                  data.push(item);
                }
              }
            }

            var allCustomKpis = settings.getOrg(self.dbName + '.c');
            allCustomKpis = allCustomKpis ? allCustomKpis : [];

            for (var k = 0; k < allCustomKpis.length; k++) {
              if (ng.isArray(allCustomKpis[k].kpis))
                for (var l = 0; l < allCustomKpis[k].kpis.length; l++) {
                  var customKpi = allCustomKpis[k].kpis[l];
                  var customItem = {code: customKpi.code};
                  customItem.show = hidden.indexOf(customKpi.code) < 0;
                  data.push(customItem);
                }
            }
          };

          function handleVisibleKpis(kpis, visibleKpis) {
            var newVisibleKpis = [];
            if (ng.isArray(kpis))
              for (var i = 0; i < kpis.length; i++) {
                if (ng.isArray(kpis[i].kpis)) {
                  for (var j = 0; j < kpis[i].kpis.length; j++) {
                    var kpi = kpis[i].kpis[j];
                    if (!KpiDefinitionsEditorFormatter.byCode(kpi.code)) {
                      continue;
                    }
                    var item = {code: kpi.code};
                    if (visibleKpis.indexOf(kpi.code) > -1) {
                      newVisibleKpis.push(kpi.code);
                      item.show = true;
                    } else {
                      item.show = false;
                    }
                    data.push(item);
                  }
                }
              }
            return newVisibleKpis;
          }

          this.initializeVisible = function (isAdmin) {
            self.options.editable = isAdmin;
            data = [];
            var dest = CF.get(scope, saveDestinationName);
            var visibleKpis = ng.copy(dest && ng.isArray(dest.visibleKpis) ? dest.visibleKpis : []);
            dest.visibleKpis = [];
            Array.prototype.push.apply(dest.visibleKpis, handleVisibleKpis(settings.getOrg(self.dbName), visibleKpis));
            Array.prototype.push.apply(dest.visibleKpis, handleVisibleKpis(settings.getOrg(self.dbName + '.c'), visibleKpis));
            data.visibleKpis = ng.copy(dest.visibleKpis);
            self.dataVersion++;
          };

          this.onAddNewRowHidden = function (args) {
            data.addItem(args.item);
            self.onCellChangeHidden();
          };

          this.onCellChangeHidden = function (row, cell, item) {
            var retval = [];
            for (var i = 0; i < data.length; i++)
              if (!data[i].show)
                retval.push(data[i].code);
            var dest = CF.get(scope, saveDestinationName);
            dest [self.dbName + self.dbPostfix] = retval;
          };

          this.onAddNewRowVisible = function (args) {
            data.addItem(args.item);
            self.onCellChangeVisible();
          };

          this.onCellChangeVisible = function (row, cell, item) {
            var retval = [];
            for (var i = 0; i < data.length; i++)
              if (data[i].show)
                retval.push(data[i].code);
            var dest = CF.get(scope, saveDestinationName);
            dest.visibleKpis = retval;
            data.visibleKpis = ng.copy(dest.visibleKpis);
          };

          this.getVisibleKpis = function () {
            return data.visibleKpis;
          };

        }


        function getReportTypesData(scope, saveDestinationName, rTypes, extendData) {
          var res = [];
          rTypes = !rTypes ? reportTypes : rTypes;
          for (i = 0; i < rTypes.length; i++) {
            res.push(new ReportTypeSettingsData(
              ReportTypeSettingColumns, rTypes[i].name, rTypes[i].db, _hiddenPostfix, scope, saveDestinationName
            ));
            res.style = {width: (100 / rTypes.length) + '%'};
          }
          return res;
        }


        function registerCodeFormatterWatch(scope, reportTypesData, columns) {
          CF.callWhenTrue(function () {
            return KpiDefinitionsEditorFormatter.isLoaded();
          }, scope, function () {

            CF.setColumn(columns, 'code', 'formatter', KpiDefinitionsEditorFormatter.formatter);
            if (ng.isArray(reportTypesData)) {
              for (i = 0; i < reportTypesData.length; i++) {
                if (ng.isFunction(reportTypesData[i].invalidator.invalidate))
                  reportTypesData[i].invalidator.invalidate();
              }
            } else {
              if (ng.isFunction(reportTypesData.invalidator.invalidate))
                reportTypesData.invalidator.invalidate();
            }
          });
        }
      }
    ])
    .factory('CustomReportSetuperGlobal', ['reportTypesFactory', 'reportLocationsFactory', '$timeout', '$filter', function (reportTypesFactory, reportLocationsFactory, $timeout, $filter) {
      return {
        initReportTypes: initReportTypes,
        initReportLocations: initReportLocations,
        locationWatchCallback: locationWatchCallback
      };
      function initReportTypes(scope, oldValue, from, to, exclude) {
        var theValue;
        var contextDate = from && moment(from).toDate();
        var types = reportTypesFactory(scope.reportLocations, contextDate, to, true, true, true, true, false, true);
        if (ng.isArray(exclude))
          types = types.filter(function (item) {
            return item.id && exclude.indexOf(item.id) < 0;
          });
        scope.reportAnimalTypes = types;
        scope.reportAnimalTypesMap = CF.createHashObject(scope.reportAnimalTypes, 'id');

        theValue = scope.reportAnimalTypesMap[oldValue] && scope.reportAnimalTypesMap[oldValue].id;
        scope.global.type = theValue || scope.reportAnimalTypes[0].id;
      }

      function initReportLocations(scope, oldValue, from, to) {
        var theValues = [];
        var contextDate = from && moment(from).toDate();
        var animalType = scope.reportAnimalTypesMap[scope.global.type];

        scope.locations = reportLocationsFactory(scope.reportLocations, scope.userInfo.farmName, animalType, contextDate);
        scope.locationsMap = CF.createHashObject(scope.locations, 'id');

        ng.forEach(oldValue, function (theValue) {
          if (scope.locationsMap[theValue]) {
            this.push(theValue);
          }
        }, theValues);

        if (theValues.length === 0 && scope.locations.length > 0) {
          theValues.push(scope.locations[0].id);
        }


        scope.global.farm = theValues;
      }

      function locationWatchCallback(n, o, scope) {
        $timeout(function () {
          o = o || [];
          n = n || [];
          var isOrgInOldValue = $filter('filter')(o, function (v) {
            return v == -1;
          }).length;
          var isOrgInNewValue = $filter('filter')(n, function (v) {
            return v == -1;
          }).length;
          if (isOrgInOldValue && n.length > 1) {
            ng.forEach(scope.global.farm, function (locId, index) {
              if (locId === -1) {
                scope.global.farm.splice(index, 1);
              }
            });
          } else if (isOrgInNewValue && n.length > 1) {
            ng.forEach(scope.global.farm, function (locId, index) {
              if (locId === -1) {
                scope.global.farm = [locId];
              }
            });
          }
        });
      }

    }])
    .factory('DateInterpretersTests', ['DateInterpreters', function (DI) {
      //temporary solution until we start using some unit testing
      //checking whether date interpreters date methods return same dates from which the definitions were created
      //context dates have to bo later then textDates
      var ctxDates = ['2015-04-15', '2015-05-15', '2015-06-01', '2015-07-15', '2015-12-15', '2015-04-26'];
      var periodDates = ['2015-01-01', '2015-04-03', '2014-12-01', '2015-01-01', '2014-12-31', '2014-08-06'];
      ng.forEach(DI.definitions, function (def, defName) {
        for (var i = 0; i < ctxDates.length; i++) {
          for (var j = 0; j < periodDates.length; j++) {
            var obj = periodDates[j];
            var startDefinition = def(periodDates[j], ctxDates[i], true);
            var startDateFromDefinition = DI.dateFromDef(startDefinition, ctxDates[i]);
            if (periodDates[j] !== startDateFromDefinition) {
              console.error(' ctx:' + ctxDates[i] + ' input /output dates not equal - ' + periodDates[j] + '/' + startDateFromDefinition + ' def -');
              console.error(startDefinition);
            }
            var endDefinition = def(periodDates[j], ctxDates[i], false);
            var endDateFromDefinition = DI.dateFromDef(endDefinition, ctxDates[i]);
            if (periodDates[j] !== endDateFromDefinition) {
              console.error(' ctx:' + ctxDates[i] + ' input /output dates not equal - ' + periodDates[j] + '/' + endDateFromDefinition + ' def -');
              console.error(endDefinition);
            }
          }
        }
      });
    }]);
})
;
