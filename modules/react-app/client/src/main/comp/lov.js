define(['angular', 'jquery', 'messages', 'cf/utils', 'slick.core', 'slick.grid', 'comp/stuff', 'angular-ui'], function (ng, $, M, CF, Slick) {
  'use strict';

  var mod = ng.module('lov', ['ui', 'stuff', 'settings']);
  CF.addIfNotIn(mod.requires, ['data', 'ui', 'stuff', 'settings']);

  mod.factory('Farm', ['$resource', function ($resource) {
    return $resource('/api/lov/farm');
  }])
    .factory('FarmLov', ['Farm', function (Farm) {
      var loaded = false, farmById = {}, farmByName = {}, farmlov = Farm.query(function (data) {
        loaded = true;
        for (var i = 0; i < data.length; ++i) {
          farmById[data[i].id] = data[i];
          farmByName[data[i].name] = data[i];
        }
      });

      var farmAutoComplete = function (req, callback) {
        var i, abbr = req.term, result = [];
        for (i = 0; i < farmlov.length; ++i) {
          if (CF.containsCaseInsensitive(farmlov[i].name, abbr) && (farmlov[i].id == -1 || farmlov.length > 2)) {
            result.push(farmlov[i].name);
          }
        }
        callback(result);
      };

      var farmAutoCompleteWithSingleFarmLocationIncluded = function (req, callback) {
        var i, abbr = req.term, result = [];
        for (i = 0; i < farmlov.length; ++i) {
          if (CF.containsCaseInsensitive(farmlov[i].name, abbr)) {
            result.push(farmlov[i].name);
          }
        }
        callback(result);
      };

      var gridEditorDyn = function (hideSingleFarmLocation) {
        var params = {
          attrs: {'class': 'editor-text'},
          fromText: function (str) {
            return str ? farmByName[str] && farmByName[str].id : null;
          },
          asText: function (id) {
            return id ? farmById[id] && farmById[id].name : '';
          },
          postInit: function (element) {
            element.autocomplete({
              minLength: 0,
              source: hideSingleFarmLocation ? farmAutoComplete : farmAutoCompleteWithSingleFarmLocationIncluded
            });
            element.bind('keydown.nav', function (e) {
              if (e.keyCode === $.ui.keyCode.DOWN || e.keyCode === $.ui.keyCode.UP || e.keyCode === $.ui.keyCode.ENTER) {
                e.stopImmediatePropagation();
              }
            });
          }
        };
        //noinspection JSPotentiallyInvalidConstructorUsage
        return Slick.Editors.Cloudfarms.parameterized(params);
      };

      return {
        isLoaded: function () {
          return loaded;
        },
        farmNameById: function (id) {
          return farmById[id] && farmById[id].name;
        },
        idByFarmName: function (farmName) {
          return farmByName[farmName] && farmByName[farmName].id;
        },
        autoComplete: farmAutoComplete,
        autoCompleteWithSingleFarmLocationIncluded: farmAutoCompleteWithSingleFarmLocationIncluded,
        gridEditor: gridEditorDyn(true),
        gridEditorWithSingleFarmLocationIncluded: gridEditorDyn(false),
        gridFormatter: function (row, cell, value) {
          var farm = value ? farmById[value] : null;
          return farm ? '<span title="' + CF.escapeHtml(farm.name) + '">' + CF.escapeHtml(farm.name) + '</span>' : '';
        }
      };
    }])
    .directive('cfLookupFarm', ['FarmLov', function (FarmLov) {
      return {
        require: 'ngModel',
        restrict: 'C',
        link: function (scope, element, attrs, ctrl) {
          function farmParser(value) {
            if (!value || value === '') {
              ctrl.$setValidity('lookup', true);
              return;
            }
            var farmId = FarmLov.idByFarmName(value);
            ctrl.$setValidity('lookup', ng.isDefined(farmId));
            return farmId;
          }

          if (!FarmLov.isLoaded()) {
            element.attr('disabled', true);
            var cancelInitWatch = scope.$watch(FarmLov.isLoaded, function () {
              if (FarmLov.isLoaded()) {
                element.attr('disabled', false);

                var formatters = ctrl.$formatters,
                  idx = formatters.length,
                  value = ctrl.$modelValue;
                while (idx--) {
                  value = formatters[idx](value);
                }
                if (ctrl.$viewValue !== value) {
                  ctrl.$viewValue = value;
                  ctrl.$render();
                }
                ctrl.$parsers.push(farmParser);
                cancelInitWatch(); // REVIEW: Perhaps we could reaload locations, if we spot an uknown one. In such case handle this differently!
              }
            });
          } else {
            ctrl.$parsers.push(farmParser);
          }
          ctrl.$formatters.push(function (farmId) {
            if (ng.isUndefined(farmId)) {
              ctrl.$setValidity('lookup', true);
              return;
            }
            ctrl.$setValidity('lookup', !FarmLov.isLoaded() || ng.isDefined(farmId));
            return FarmLov.farmNameById(farmId);
          });
          element.autocomplete({
            minLength: 0,
            select: function (event, ui) {
              var selectedObj = ui.item;
              scope.$apply(function () {
                ctrl.$setViewValue(selectedObj.value);
              });
            },
            source: attrs.hasOwnProperty('singleFarmLocationIncluded') ? FarmLov.autoCompleteWithSingleFarmLocationIncluded : FarmLov.autoComplete,
            change: function () {
              scope.$apply(function () {
                ctrl.$setViewValue(element.val());
              });
            }
          });
        }
      };
    }]);

  mod.factory('Kpi', ['$resource', function ($resource) {
    return $resource('/api/lov/kpi');
  }])
    .factory('KpiLov', ['Kpi', '$q', '$filter', function (Kpi, $q, $filter) {
      var standardKpiDefinitions = $q.defer();
      var loaded = false, kpiById = {}, kpiByName = {}, kpisByName = {}, kpilov = Kpi.query(function (data) {
        var standardKpi = [];
        loaded = true;
        kpisByName = CF.createHashObjectOfArrays(data, 'name');
        for (var i = 0; i < data.length; ++i) {
          kpiById[data[i].id] = data[i];
          kpiByName[data[i].name] = data[i];
          if (!data[i].isCustom) {
            standardKpi.push(data[i]);
          }
        }
        standardKpi = $filter('orderBy')(standardKpi, 'name');
        standardKpiDefinitions.resolve(standardKpi);
      });

      var autocomplete = function (req, callback) {
        var i, abbr = req.term, result = [];
        for (i = 0; i < kpilov.length; ++i) {
          if (CF.containsCaseInsensitive(kpilov[i].name, abbr)) {
            result.push(kpilov[i].name);
          }
        }
        callback(result);
      };



      var gridEditor = (function () {
        var params = {
          attrs: {'class': 'editor-text'},
          fromText: function (str) {
            return str ? kpiByName[str] && kpiByName[str].id : null;
          },
          asText: function (id) {
            return id ? kpiById[id] && kpiById[id].name : '';
          },
          postInit: function (element) {
            element.autocomplete({
              minLength: 0,
              source: autocomplete
            });
            element.bind('keydown.nav', function (e) {
              if (e.keyCode === $.ui.keyCode.DOWN || e.keyCode === $.ui.keyCode.UP || e.keyCode === $.ui.keyCode.ENTER) {
                e.stopImmediatePropagation();
              }
            });
          }
        };
        //noinspection JSPotentiallyInvalidConstructorUsage
        return Slick.Editors.Cloudfarms.parameterized(params);
      })();

      return {
        standardKpis: standardKpiDefinitions.promise,
        isLoaded: function () {
          return loaded;
        },
        kpiNameById: function (id) {
          return kpiById[id] && kpiById[id].name;
        },
        idsByKpiName: function (kpiName) {
          return kpisByName[kpiName] && (kpisByName[kpiName].map(function(i) {return i.id;}).join(' ') + ' ');
        },
        idByKpiName: function (kpiName) {
          return kpiByName[kpiName] && kpiByName[kpiName].id;
        },
        autocomplete: autocomplete,
        gridEditor: gridEditor,
        gridFormatter: function (row, cell, value) {
          var kpi = value ? kpiById[value] : null;
          return kpi ? '<span title="' + CF.escapeHtml(kpi.name) + '">' + CF.escapeHtml(kpi.name) + '</span>' : '';
        }
      };
    }])
    .directive('cfLookupKpi', ['KpiLov', function (KpiLov) {
      return {
        require: 'ngModel',
        restrict: 'C',
        link: function (scope, element, attrs, ctrl) {
          function kpiParser(value) {
            if (!value || value === '') {
              ctrl.$setValidity('lookup', true);
              return;
            }
            var kpiId = KpiLov.idsByKpiName(value);
            ctrl.$setValidity('lookup', ng.isDefined(kpiId));
            return kpiId;
          }

          if (!KpiLov.isLoaded()) {
            element.attr('disabled', true);
            var cancelInitWatch = scope.$watch(KpiLov.isLoaded, function () {
              if (KpiLov.isLoaded()) {
                element.attr('disabled', false);

                var formatters = ctrl.$formatters,
                  idx = formatters.length,
                  value = ctrl.$modelValue;
                while (idx--) {
                  value = formatters[idx](value);
                }
                if (ctrl.$viewValue !== value) {
                  ctrl.$viewValue = value;
                  ctrl.$render();
                }
                ctrl.$parsers.push(kpiParser);
                cancelInitWatch(); // REVIEW: Perhaps we could reaload locations, if we spot an uknown one. In such case handle this differently!
              }
            });
          } else {
            ctrl.$parsers.push(kpiParser);
          }
          ctrl.$formatters.push(function (kpiId) {
            if (!kpiId) {
              ctrl.$setValidity('lookup', true);
              return;
            }
            ctrl.$setValidity('lookup', !KpiLov.isLoaded() || ng.isDefined(kpiId));
            return KpiLov.kpiNameById(kpiId.split(' ')[0]);
          });
          element.autocomplete({
            minLength: 0,
            select: function (event, ui) {
              var selectedObj = ui.item;
              scope.$apply(function () {
                ctrl.$setViewValue(selectedObj.value);
              });
            },
            source: KpiLov.autocomplete,
            change: function () {
              scope.$apply(function () {
                ctrl.$setViewValue(element.val());
              });
            }
          });
        }
      };
    }]);

  mod.factory('DKCRTransferLov', function () {
    var dkcrTransferLov = [
      {id: 'din', name: M('js.label.dkcrtransfer.din')},
      {id: 'exp', name: M('js.label.dkcrtransfer.exp')},
      {id: 'imp', name: M('js.label.dkcrtransfer.imp')}
    ];
    var dkcrTransferById = {}, dkcrTransferByName = {};

    for (var i = 0; i < dkcrTransferLov.length; ++i) {
      dkcrTransferById[dkcrTransferLov[i].id] = dkcrTransferLov[i];
      dkcrTransferByName[dkcrTransferLov[i].name] = dkcrTransferLov[i];
    }

    var autocomplete = function (req, callback) {
      var i, abbr = req.term, result = [];
      for (i = 0; i < dkcrTransferLov.length; ++i) {
        if (CF.containsCaseInsensitive(dkcrTransferLov[i].name, abbr)) {
          result.push(dkcrTransferLov[i].name);
        }
      }
      callback(result);
    };

    var gridEditor = (function () {
      var params = {
        attrs: {'class': 'editor-text'},
        fromText: function (str) {
          return str ? dkcrTransferByName[str] && dkcrTransferByName[str].id : null;
        },
        asText: function (id) {
          return id ? dkcrTransferById[id] && dkcrTransferById[id].name : '';
        },
        postInit: function (element) {
          element.autocomplete({
            minLength: 0,
            source: autocomplete
          });
          element.bind('keydown.nav', function (e) {
            if (e.keyCode === $.ui.keyCode.DOWN || e.keyCode === $.ui.keyCode.UP || e.keyCode === $.ui.keyCode.ENTER) {
              e.stopImmediatePropagation();
            }
          });
        }
      };
      //noinspection JSPotentiallyInvalidConstructorUsage
      return Slick.Editors.Cloudfarms.parameterized(params);
    })();

    return {
      isLoaded: function () {
        return true;
      },
      dkcrTransferNameById: function (id) {
        return dkcrTransferById[id] && dkcrTransferById[id].name;
      },
      idByDKCRTransferName: function (dkcrTransferName) {
        return dkcrTransferByName[dkcrTransferName] && dkcrTransferByName[dkcrTransferName].id;
      },
      autocomplete: autocomplete,
      gridEditor: gridEditor,
      gridFormatter: function (row, cell, value) {
        var dkcrTransfer = value ? dkcrTransferById[value] : null;
        return dkcrTransfer ? '<span title="' + CF.escapeHtml(dkcrTransfer.name) + '">' + CF.escapeHtml(dkcrTransfer.name) + '</span>' : '';
      }
    };
  })
    .directive('cfLookupDkcrtransfer', ['DKCRTransferLov', function (DKCRTransferLov) {
      return {
        require: 'ngModel',
        restrict: 'C',
        link: function (scope, element, attrs, ctrl) {
          function dkcrTransferParser(value) {
            if (!value || value === '') {
              ctrl.$setValidity('lookup', true);
              return;
            }
            var dkcrTransferId = DKCRTransferLov.idByDKCRTransferName(value);
            ctrl.$setValidity('lookup', ng.isDefined(dkcrTransferId));
            return dkcrTransferId;
          }

          if (!DKCRTransferLov.isLoaded()) {
            element.attr('disabled', true);
            var cancelInitWatch = scope.$watch(DKCRTransferLov.isLoaded, function () {
              if (DKCRTransferLov.isLoaded()) {
                element.attr('disabled', false);

                var formatters = ctrl.$formatters,
                  idx = formatters.length,
                  value = ctrl.$modelValue;
                while (idx--) {
                  value = formatters[idx](value);
                }
                if (ctrl.$viewValue !== value) {
                  ctrl.$viewValue = value;
                  ctrl.$render();
                }
                ctrl.$parsers.push(dkcrTransferParser);
                cancelInitWatch(); // REVIEW: Perhaps we could reaload locations, if we spot an uknown one. In such case handle this differently!
              }
            });
          } else {
            ctrl.$parsers.push(dkcrTransferParser);
          }
          ctrl.$formatters.push(function (dkcrTransferId) {
            if (!dkcrTransferId) {
              ctrl.$setValidity('lookup', true);
              return;
            }
            ctrl.$setValidity('lookup', !DKCRTransferLov.isLoaded() || ng.isDefined(dkcrTransferId));
            return DKCRTransferLov.dkcrTransferNameById(dkcrTransferId);
          });
          element.autocomplete({
            minLength: 0,
            select: function (event, ui) {
              var selectedObj = ui.item;
              scope.$apply(function () {
                ctrl.$setViewValue(selectedObj.value);
              });
            },
            source: DKCRTransferLov.autocomplete,
            change: function () {
              scope.$apply(function () {
                ctrl.$setViewValue(element.val());
              });
            }
          });
        }
      };
    }]);

  mod.factory('CountriesDataSource', ['$resource', function ($resource) {
    var values = [];
    var Country = $resource('/api/countries');
    var valuesById = [];

    var query = Country.query(function (data) {
      for (var i = 0; i < data.length; ++i) {
        var pureCountry = {id: data[i].id, name: data[i].name};
        values.push(pureCountry);
        valuesById[data[i].id] = pureCountry;
      }
    });
    return {
      getQuery: function () {
        return query;
      },
      getLength: function () {
        return values.length;
      },
      getItem: function (i) {
        return values[i];
      },
      getValue: function (id) {
        return valuesById[id];
      },
      formatter: function (value) {
        return ng.isObject(value) ? value.id + ' (' + value.name + ')' : undefined;
      },
      gridFormatter: function (row, cell, value) {
        if (!value) return '';
        var result = valuesById[value];
        return !result ? '' : result.name + ' (' + result.id + ')';
      },
      gridEditor: (function () {
        var dataSource = {
          getLength: function () {return values.length;},
          getItem: function (i) {return values[i];}
        };
        return Slick.Editors.Cloudfarms.selectEditor(dataSource, 'id', function (value) { return (!value ? '' : value.name + ' (' + value.id + ')'); }, false);
      })()
    };
  }])
    .factory('CountriesGridFormatter', ['CountriesDataSource', function (CountriesDataSource) {
      return function (row, cell, value) {
        var id = ng.isObject(value) ? value.id : value;
        var country = CountriesDataSource.getValue(id);

        return country ? '<span title="' + CF.escapeHtml(country.name) + '">' + CF.escapeHtml(country.id) + ' (' + CF.escapeHtml(country.name) + ')</span>' : '';
      };
    }]);

  mod.factory('OrganizationsDataSource', ['$resource', function ($resource) {
    var values = [{id: null, name: ""}];
    var Organization = $resource('/api/business/organizations');
    var valuesById = [];
    var isLoaded = false;

    var query = Organization.query(function (data) {
      for (var i = 0; i < data.length; ++i) {
        var pureOrganization = {id: data[i].id, name: data[i].name};
        values.push(pureOrganization);
        valuesById[data[i].id] = pureOrganization;
      }
      isLoaded = true;
    });
    return {
      getQuery: function () {
        return query;
      },
      getLength: function () {
        return values.length;
      },
      getItem: function (i) {
        return values[i];
      },
      getValue: function (id) {
        return valuesById[id];
      },
      formatter: function (value) {
        return ng.isObject(value) ? value.id + ' (' + value.name + ')' : undefined;
      },
      gridFormatter: function (row, cell, value) {
        if (!value) return '';
        var result = valuesById[value];
        return !result ? '' : result.name;
      },
      idByOrgName: function(value) {
        for (var i = 0; i < values.length; i++) {
          if (values[i].name === value)
            return values[i].id
        }
        return null
      },
      isLoaded: function() {
        return isLoaded;
      },
      autocompleteFactory: function() {
        return function (req, callback) {
          var i, abbr = (req.term || '').toLowerCase(), result = [];
          for (i = 0; i < values.length; ++i) {
            var org = values[i];
            if (CF.startsWith(org.name.toLowerCase(), abbr)) {
              result.push({
                value: org.name,
                label: org.name
              });
            }
          }
          result.sort(function (a, b) {
            return CF.mixedComparator(a.label, b.label);
          });
          callback(result);
        }
      },
      gridEditor: (function () {
        var dataSource = {
          getLength: function () {return values.length;},
          getItem: function (i) {return values[i];}
        };
        return Slick.Editors.Cloudfarms.selectEditor(dataSource, 'id', function (value) { return (!value ? '' : value.name); }, false);
      })()
    };
  }])
    .factory('OrganizationsGridFormatter', ['OrganizationsDataSource', function (OrganizationsDataSource) {
      return function (row, cell, value) {
        var id = ng.isObject(value) ? value.id : value;
        var organization = OrganizationsDataSource.getValue(id);
        return organization ? '<span title="' + CF.escapeHtml(organization.name) + '">' + CF.escapeHtml(organization.name) + '</span>' : '';
      };
    }]);

  mod.factory('FeedSystemLov', ['$resource',
    function ($resource) {
      var loaded = false, byId = {}, byName = {};
      $resource('/api/feed/system').query(function (data) {
        loaded = true;
        for (var i = 0; i < data.length; ++i) {
          byId[data[i].id] = data[i];
          byName[data[i].name] = data[i];
        }
      });

      var autoComplete = function (req, callback) {
        var i, abbr = req.term, result = [];
        ng.forEach(byName, function(value, key) {
          if (CF.containsCaseInsensitive(key, abbr)) {
            result.push(key);
          }
        });
        callback(result);
      };

      var removeValue = function(item) {
        delete byId[item.id];
        delete byName[item.name];
      };

      var addValue = function(item) {
        byId[item.id] = item;
        byName[item.name] = item;
      };

      var gridEditor = (function () {
        var params = {
          attrs: {'class': 'editor-text'},
          fromText: function (str) {
            return str ? byName[str] && byName[str].id : null;
          },
          asText: function (id) {
            return id ? byId[id] && byId[id].name : '';
          },
          postInit: function (element) {
            element.autocomplete({
              minLength: 0,
              source: autoComplete
            });
            element.bind('keydown.nav', function (e) {
              if (e.keyCode === $.ui.keyCode.DOWN || e.keyCode === $.ui.keyCode.UP || e.keyCode === $.ui.keyCode.ENTER) {
                e.stopImmediatePropagation();
              }
            });
          }
        };
        return Slick.Editors.Cloudfarms.parameterized(params);
      })();

      return {
        isLoaded: function () {
          return loaded;
        },
        nameById: function (id) {
          return byId[id] && byId[id].name;
        },
        idByName: function (name) {
          return byName[name] && byName[name].id;
        },
        typeById: function (id) {
          return byId[id] && byId[id].type;
        },
        addValue: addValue,
        removeValue: removeValue,
        autoComplete: autoComplete,
        gridEditor: gridEditor,
        gridFormatter: function (row, cell, value) {
          var item = value ? byId[value] : null;
          return item ? '<span title="' + CF.escapeHtml(item.name) + '">' + CF.escapeHtml(item.name) + '</span>' : '';
        }
      };
    }])
    .directive('cfLookupFeedSystem', ['FeedSystemLov', function (FeedSystemLov) {
      return {
        require: 'ngModel',
        restrict: 'C',
        link: function (scope, element, attrs, ctrl) {
          function theParser(value) {
            if (!value || value === '') {
              ctrl.$setValidity('lookup', true);
              return;
            }
            var id = FeedSystemLov.idByName(value);
            ctrl.$setValidity('lookup', ng.isDefined(id));
            return id;
          }

          if (!FeedSystemLov.isLoaded()) {
            element.attr('disabled', true);
            var cancelInitWatch = scope.$watch(FeedSystemLov.isLoaded, function () {
              if (FeedSystemLov.isLoaded()) {
                element.attr('disabled', false);

                var formatters = ctrl.$formatters,
                  idx = formatters.length,
                  value = ctrl.$modelValue;
                while (idx--) {
                  value = formatters[idx](value);
                }
                if (ctrl.$viewValue !== value) {
                  ctrl.$viewValue = value;
                  ctrl.$render();
                }
                ctrl.$parsers.push(theParser);
                cancelInitWatch();
              }
            });
          } else {
            ctrl.$parsers.push(theParser);
          }
          ctrl.$formatters.push(function (id) {
            if (!id) {
              ctrl.$setValidity('lookup', true);
              return;
            }
            ctrl.$setValidity('lookup', !FeedSystemLov.isLoaded() || ng.isDefined(id));
            return FeedSystemLov.nameById(id);
          });
          element.autocomplete({
            minLength: 0,
            select: function (event, ui) {
              var selectedObj = ui.item;
              scope.$apply(function () {
                ctrl.$setViewValue(selectedObj.value);
              });
            },
            source: FeedSystemLov.autoComplete,
            change: function () {
              scope.$apply(function () {
                ctrl.$setViewValue(element.val());
              });
            }
          });
        }
      };
    }]);

  mod.factory('FeedStationLov', ['$resource',
    function ($resource) {
      var loaded = false, byId = {}, byName = {}, feedStations = $resource('/api/feed/station').query(function (data) {
        loaded = true;
        for (var i = 0; i < data.length; ++i) {
          byId[data[i].id] = data[i];
          byName[data[i].name] = data[i];
        }
      });

      var autoComplete = function (req, callback) {
        var i, abbr = req.term, result = [];
        for (i = 0; i < feedStations.length; ++i) {
          if (CF.containsCaseInsensitive(feedStations[i].name, abbr)) {
            result.push(feedStations[i].name);
          }
        }
        callback(result);
      };

      var gridEditor = (function () {
        var params = {
          attrs: {'class': 'editor-text'},
          fromText: function (str) {
            return str ? byName[str] && byName[str].id : null;
          },
          asText: function (id) {
            return id ? byId[id] && byId[id].name : '';
          },
          postInit: function (element) {
            element.autocomplete({
              minLength: 0,
              source: autoComplete
            });
            element.bind('keydown.nav', function (e) {
              if (e.keyCode === $.ui.keyCode.DOWN || e.keyCode === $.ui.keyCode.UP || e.keyCode === $.ui.keyCode.ENTER) {
                e.stopImmediatePropagation();
              }
            });
          }
        };
        return Slick.Editors.Cloudfarms.parameterized(params);
      })();

      return {
        isLoaded: function () {
          return loaded;
        },
        nameById: function (id) {
          return byId[id] && byId[id].name;
        },
        idByName: function (name) {
          return byName[name] && byName[name].id;
        },
        autoComplete: autoComplete,
        gridEditor: gridEditor,
        gridFormatter: function (row, cell, value) {
          var item = value ? byId[value] : null;
          return item ? '<span title="' + CF.escapeHtml(item.name) + '">' + CF.escapeHtml(item.name) + '</span>' : '';
        }
      };
    }])
    .directive('cfLookupFeedStation', ['FeedStationLov', function (FeedStationLov) {
      return {
        require: 'ngModel',
        restrict: 'C',
        link: function (scope, element, attrs, ctrl) {
          function theParser(value) {
            if (!value || value === '') {
              ctrl.$setValidity('lookup', true);
              return;
            }
            var id = FeedStationLov.idByName(value);
            ctrl.$setValidity('lookup', ng.isDefined(id));
            return id;
          }

          if (!FeedStationLov.isLoaded()) {
            element.attr('disabled', true);
            var cancelInitWatch = scope.$watch(FeedStationLov.isLoaded, function () {
              if (FeedStationLov.isLoaded()) {
                element.attr('disabled', false);

                var formatters = ctrl.$formatters,
                  idx = formatters.length,
                  value = ctrl.$modelValue;
                while (idx--) {
                  value = formatters[idx](value);
                }
                if (ctrl.$viewValue !== value) {
                  ctrl.$viewValue = value;
                  ctrl.$render();
                }
                ctrl.$parsers.push(theParser);
                cancelInitWatch();
              }
            });
          } else {
            ctrl.$parsers.push(theParser);
          }
          ctrl.$formatters.push(function (id) {
            if (!id) {
              ctrl.$setValidity('lookup', true);
              return;
            }
            ctrl.$setValidity('lookup', !FeedStationLov.isLoaded() || ng.isDefined(id));
            return FeedStationLov.nameById(id);
          });
          element.autocomplete({
            minLength: 0,
            select: function (event, ui) {
              var selectedObj = ui.item;
              scope.$apply(function () {
                ctrl.$setViewValue(selectedObj.value);
              });
            },
            source: FeedStationLov.autoComplete,
            change: function () {
              scope.$apply(function () {
                ctrl.$setViewValue(element.val());
              });
            }
          });
        }
      };
    }]);
});
