package reactapp.client.pages.batches

import java.time.Instant
import autowire._
import domainz.location.InhabitantTypeEnum
import japgolly.scalajs.react.vdom.html_<^._
import japgolly.scalajs.react.Callback
import org.scalajs.dom
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.common.component.HoverMenu2.DeleteButton2
import reactapp.client.common.{ AutowireClient, CfContext }
import reactapp.client.facades.M
import reactapp.shared.batches.detail.DeadGrid.ValidationProps
import reactapp.shared.batches.{ BatchesGrid, BatchesGridColumn, BatchesPageApi }
import reactapp.shared.batches.detail.{ DeadGrid, DeadGridApi }
import sjsgrid.client.common.react.ExtVar
import sjsgrid.client.grid.params.OnSaveParams
import sjsgrid.client.grid.{ GridBuilder, GridRow, SavingHandler }
import sjsgrid.shared.form.types.InstantFieldType.TargetInstant
import sjsgrid.shared.grid.column.ColumnsState

import org.scalajs.macrotaskexecutor.MacrotaskExecutor.Implicits._
import scala.concurrent.Future
import scala.util.{ Failure, Success }
import sjsgrid.shared.common.PushkaPicklers._

case class DeadSection(
  cfContext: CfContext,
  batchRowEV: ExtVar[BatchesPage.GridRowType],
  columnsStateEV: ExtVar[ColumnsState[DeadGrid]],
  savingHandler: SavingHandler,
) {
  def apply(): VdomElement = DeadSection.gridCmp(this)
}

object DeadSection {
  type State = Option[Int]

  def onSave(params: OnSaveParams[DeadSection, Option[Int], DeadGrid, Option[Long]]): Option[Future[Unit]] = {
    params.props.batchRowEV.value.additionalData.serverBatchIdOpt.map { batchId =>
      AutowireClient[DeadGridApi].save(batchId, params.unsavedRows.rowsToSaveDTO).call()
        .map { results =>
          params.props.batchRowEV.modCb(_.modAdditionalData(_.invalidateOverviewSection)).runNow()
          params.gridModelEV.modCb(_.savedRowIds(results)).runNow()
        }
    }
  }

  private val gridCmp = GridBuilder[DeadSection, DeadGrid]("DeadGrid", DeadGrid)
    .pageContextGetter(_.cfContext)
    .initialState(Option.empty[Int])
    .getGridModelEVFromPropsPot(_.batchRowEV.zoomL(GridRow.additionalData).zoomL(BatchAD.deadPot))
    .inMemory
    .cellRendererOverride(params => {
      case DeadGrid.AnimalId(msgOpt) =>
        val tooltip = DeadGrid.AnimalId.tooltip(params.pageContext)
        s"""<span data-tooltip="" title="$tooltip">${msgOpt.getOrElse("")}</span>"""
    })
    .permanentIdGetter(row => row.additionalData)
    .bindSelectedRowId(_.stateEV)
    .bindColumnsState(_.props.columnsStateEV)
    .keepEmptyNewRow(params => DeadGrid.unsavedRow2(None, params.props.cfContext))
    .onSave(onSave, _.savingHandler)
    .onSelectedRowModification { params =>
      val batchRow = params.props.batchRowEV.value
      params.conditionaly(
        when = row =>
          (params.prevRow.getValue(DeadGrid.Weight) != row.getValue(DeadGrid.Weight)) ||
            (params.prevRow.getValue(DeadGrid.Amount) != row.getValue(DeadGrid.Amount)),
        modify = params => {
          params.row.setValue(
            DeadGrid.AvgWeight, {
              for {
                weight <- params.row.getValue(DeadGrid.Weight)
                amount <- params.row.getValue(DeadGrid.Amount)
                if amount != 0
              } yield weight / amount
            },
          )
        },
      ).conditionaly(
        when = row =>
          DeadGrid.Age.hasReadAccess(params.props.cfContext) &&
            (params.props.batchRowEV.value.additionalData.serverBatchIdOpt.isDefined && row.getValue(DeadGrid.ActorDate).isDefined) &&
            ((params.prevRow.getValue(DeadGrid.ActorDate) != row.getValue(DeadGrid.ActorDate)) && params.prevRow.getValue(
              DeadGrid.Age,
            ).isEmpty),
        modify = prms => {
          AutowireClient[BatchesPageApi].getAverageExitAgeOpt(
            prms.props.batchRowEV.value.additionalData.serverBatchIdOpt.toSeq,
            prms.row.getValue(DeadGrid.ActorDate).get,
          ).call() onComplete {
            case Success(ageOpts) =>
              prms.rowSetter.modCb(_.setValue(DeadGrid.Age, ageOpts.headOption.map { case (_, age) => age })).runNow()
            case Failure(_) => log("batch avg age calc failed")
          }
          prms.row
        },
      ).row
        .validated(DeadGrid)(deadRow => {
          BatchesPage.commonValidationProps(
            batchRow,
            deadRow.getValue(DeadGrid.AnimalId).isDefined || deadRow.getValue(DeadGrid.AnimalName).isDefined,
          )
        })

    }
    .render { params =>
      if (!params.props.batchRowEV.value.storedOnServer) {
        <.div(
          M("error.savebeforeedit", M("js.title.batches.dead")),
        )
      } else {
        <.div(
          ^.cls := "FlexLayout FlexLayout-Column",
          ^.height := "100%",
          ^.padding := "0",
          <.div(
            ^.cls := "ag-fresh columns GridParent",
            ^.minWidth := "250px",
            ^.minHeight := "250px",
            ^.height := "100%",
            ^.padding := "0",
            <.div(
              ^.paddingLeft := "40px",
              ^.paddingRight := "0",
              ^.overflowX := "hidden",
              ^.overflowY := "hidden",
              ^.height := "100%",
              params.gridElement,
              params.hoverMenu(
                showSaveBtn = true,
                deleteBtnFnOpt = Some((rowId, row) => {
                  row.additionalData match {
                    case Some(deadId) =>
                      if (row.getValue(DeadGrid.AnimalName).isDefined || row.getValue(DeadGrid.AnimalId).isDefined) {
                        // Delete row of individual animal is disabled
                        DeleteButton2.Disabled
                      } else {
                        DeleteButton2.OnClickCb(Callback {
                          if (dom.window.confirm(M("js.prompt.confirm.question"))) {

                            AutowireClient[DeadGridApi].delete(deadId).call().onComplete {
                              case Success(_) =>
                                params.gridModelEV.modCb(_.removeRow(rowId)).runNow()
                                params.props.batchRowEV.zoomL(GridRow.additionalData).modCb(_.invalidateOverviewSection).runNow() // invalidate overview section data

                              case Failure(_) =>
                                dom.window.alert(M("error.failed.to.delete", M("js.label.death")))
                            }
                          }
                        })
                      }
                    case None =>
                      DeleteButton2.OnClickCb(Callback {
                        params.gridModelEV.modCb(_.removeRow(rowId)).runNow()
                      })
                  }
                }),
              ),
            ),
          ),
        )
      }
    }
    .build()
}
