package reactapp.client.common.component

import org.scalajs.dom
import reactapp.client.common.CloudFarmsOps.{ cssClass, _ }
import reactapp.client.facades.M
import sjsgrid.flash.client.facade.AntJs
import slinky.core.ComponentWrapper
import slinky.core.facade.{ ErrorBoundaryInfo, ReactElement }
import slinky.web.html_<^.{ <, ^ }

import scala.scalajs.js

/**
  * Created by Milan Satala
  * Date: 26. 5. 2020
  * Time: 15:09
  */
object ErrorBoundary {

  private object Cmp extends ComponentWrapper {
    type Props = () => ReactElement

    case class State(hasError: Boolean)

    class Def(jsProps: js.Object) extends Definition(jsProps) {
      def initialState = State(hasError = false)

      override def componentDidCatch(error: js.Error, info: ErrorBoundaryInfo): Unit = {
        dom.console.error(error)
        setState(State(hasError = true))
      }

      override def render(): ReactElement = {
        if (state.hasError) {
          Alert
        } else {
          try
            props()
          catch {
            case e: Exception =>
              e.printStackTrace()
              Alert
          }
        }
      }
    }

  }

  def apply(content: => ReactElement): ReactElement = Cmp(() => content)

  def Alert = <.div(
    ^.className := cssClass(
      "Error wrapper",
      """& {
                 flex: 1;
                 display: flex;
                 justify-content: center;
                 align-items: center;
               }""",
    ),
    AntJs.Alert(o(
      message = M("js.feedback.error.title"),
      description = <.span(^.dangerouslySetInnerHTML := o(__html = M("js.feedback.error.descNoSave"))),
      `type` = "error",
      showIcon = true,
    )),
  )

}
