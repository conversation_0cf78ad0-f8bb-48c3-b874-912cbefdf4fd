package reactapp.client.pages.feed.silos

import autowire._
import japgolly.scalajs.react.vdom.VdomElement
import monocle.macros.Lenses
import org.scalajs.macrotaskexecutor.MacrotaskExecutor.Implicits._
import pushka.annotation.pushka
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.common.component.HoverMenu2.DeleteButton2
import reactapp.client.common.component.{ ContentLayout2, HoverMenu2, PageLayout2 }
import reactapp.client.common.grid.views.ViewsState
import reactapp.client.common.{ AutowireClient, CfContext, CloudFarmsOps }
import reactapp.client.facades.M
import reactapp.shared.feed.silos.{ SiloCleaningApi, SiloEmptyingGrid }
import reactapp.shared.view.ViewId
import sjsgrid.client.grid.params.{ GridRenderParams, OnSaveParams }
import sjsgrid.client.grid.{ GridBuilder, GridModel }
import sjsgrid.shared.grid.column.ColumnsState
import sjsgrid.shared.grid.dto.GridSorting

import scala.concurrent.Future

object SiloCleaningPage {

  type Props = CfContext

  @pushka
  @Lenses
  case class UrlState(view: View = View(), selectedRowIndexOpt: Option[Int] = None, selectedViewIdOpt: Option[ViewId] = None)

  @pushka
  @Lenses
  case class View(
    columnsState: ColumnsState[SiloEmptyingGrid] = SiloEmptyingGrid.columnsState(GridSorting(SiloEmptyingGrid.Name)),
  )

  @Lenses
  case class State(
    viewsState: ViewsState[View],
    gridModel: GridModel[SiloEmptyingGrid, Option[Long]] = SiloEmptyingGrid.pendingModel,
    selectedRowIdOpt: Option[Int] = None,
    urlState: UrlState = UrlState(),
  )

  def render(params: GridRenderParams[CfContext, State, SiloEmptyingGrid, Option[Long]]): VdomElement = {
    PageLayout2.headerAndPositionPage(
      pageTitle = M("js.title.siloemptying"),
      position = params.indexSlashCount,
      content = ContentLayout2.fullscreenGrid(
        hoverMenu = params.hoverMenu(
          exportElOpt =
            Some(HoverMenu2.exportWithSubMenu(s"/export/silo-cleaning?columnsState=${CloudFarmsOps.writeToParam(params.state.urlState.view.columnsState)}")),
          deleteBtnFnOpt = Some((_, row) => {
            row.additionalData match {
              case Some(id) => DeleteButton2.Confirm(() => AutowireClient[SiloCleaningApi].delete(id).call())
              case None     => DeleteButton2.RemoveFromGrid
            }
          }),
        ),
        grid = params.gridElement,
      ),
    )
  }

  def onSave(params: OnSaveParams[Props, State, SiloEmptyingGrid, Option[Long]]): Some[Future[Unit]] = Some {
    AutowireClient[SiloCleaningApi].update(params.unsavedRows.rowsToSave.map(_.toDTO)).call() map { result =>
      params.gridModelEV.modCb(_.savedRowIds(result)).runNow()
    }
  }

  GridBuilder
    .rootComponent("SiloCleaningPage", SiloEmptyingGrid)
    .initialStateFromProps(cfContext => State(ViewsState(cfContext.getAvailableViews[View](SiloEmptyingGrid.viewName))))
    .getGridModelEVFromState[Option[Long]](State.gridModel)
    .virtualPaging(
      countGetter = params => AutowireClient[SiloCleaningApi].count(params.filteringAndSorting.filters).call(),
      dataGetter = params => {
        AutowireClient[SiloCleaningApi].list(params).call().map(serverRows => {
          serverRows.map {
            case (id, row) =>
              row.rowModel.toGridRow(Some(id), params.pageContext, params.gridDefinition)
          }
        })
      },
    )
    .bindUrlState(State.urlState)
    .rememberState
    .withView[View](
      currentViewL = State.urlState composeLens UrlState.view,
      viewsStateL = State.viewsState,
      selectedViewL = State.urlState composeLens UrlState.selectedViewIdOpt,
      columnsStateL = View.columnsState,
    )
    .bindSelectedRowId(_.stateEV.zoomL(State.selectedRowIdOpt))
    .bindSelectedRowIndex(_.stateEV.zoomL(State.urlState).zoomL(UrlState.selectedRowIndexOpt))
    .onSave(onSave)
    .keepEmptyNewRow(params => SiloEmptyingGrid.unsavedRow2[Option[Long]](None, params.pageContext))
    .render(render)
    .build()
}
