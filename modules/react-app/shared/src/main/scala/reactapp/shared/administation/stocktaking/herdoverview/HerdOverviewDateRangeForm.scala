package reactapp.shared.administation.stocktaking.herdoverview

import java.time.LocalDate

import reactapp.shared.common.form.FormField
import reactapp.shared.grid.Field
import sjsgrid.shared.form.types.LocalDateFieldType.{ LocalDateConf, TargetLocalDate }
import sjsgrid.shared.form.{ FieldType, FormDefinition }

import scala.collection.immutable

/**
  * Created by <PERSON>
  * Date: 27.5.2019
  * Time: 11:49
  */
sealed trait HerdOverviewDateRangeForm extends FormField

object HerdOverviewDateRangeForm extends FormDefinition[HerdOverviewDateRangeForm] {

  case object StartDate extends HerdOverviewDateRangeForm with FieldType.LocalDate with Field.Editable {
    override lazy val defaultConf = LocalDateConf(validUntil = {
      TargetLocalDate.Column(
        col = EndDate,
        inclusive = true,
      )
    })
  }

  case object EndDate extends HerdOverviewDateRangeForm with FieldType.LocalDate with Field.Mandatory {
    override lazy val defaultConf = LocalDateConf(
      validFrom = TargetLocalDate.Column(
        col = StartDate,
        inclusive = true,
      ),
      validUntil = TargetLocalDate.Value(LocalDate.MAX, true),
    )
  }

  override def values: immutable.IndexedSeq[HerdOverviewDateRangeForm] = findValues

  val cssId = "HerdOverviewDateRangeForm"

}
