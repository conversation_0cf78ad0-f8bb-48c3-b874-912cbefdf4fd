package reactapp.shared

import pushka.annotation.pushka
import reactapp.shared.common.grid.{ IllnessTypeData, MedicineData }
import reactapp.shared.medicine.{ CureGrid, PrescriptionGrid }
import sjsgrid.shared.grid.dto.{ DataGetterParams, FilteringAndSorting, RowModel, RowSaveDTO }

trait PrescriptionApi {
  def countRows(filteringAndSorting: FilteringAndSorting[PrescriptionGrid]): Int
  def list(params: DataGetterParams[PrescriptionGrid]): Seq[(Long, PrescriptionGrid.Row)]
  def getMedicineApplicationType: Seq[(String, String)]
  def updatePrescription(rowsWithSubrowsMap: PrescriptionSaveParameters): Map[Int, MainRowReturnData]
  def duplicatePrescription(medicineId: Long, prescriptionId: Long): Option[Long]
  def deletePrescription(prescriptionId: Long): Option[(String, Int, Int, Int)]
  def listCures(prescriptionId: Long): Seq[(Long, RowModel[CureGrid])]
  def deleteCure(cureId: Long): Option[(String, Int)]
  def medicineUnits(): Seq[(String, String)]
  def medicineNames(): Seq[(Long, String)]
  def medicineWithAdditionalData(): Seq[MedicineData]
  def medicineUsedInPrescriptions(): Seq[Long]
  def getIllnessTypeData: Seq[IllnessTypeData]
}

@pushka
case class RowIdAndDetailData(idOpt: Option[Long], rows: Seq[RowSaveDTO[CureGrid, Option[Long]]])
@pushka
case class MainRowReturnData(idOpt: Option[Long], detailRowIds: Map[Int, Option[Long]])
@pushka
case class PrescriptionSaveParameters(params: Seq[RowSaveDTO[PrescriptionGrid, RowIdAndDetailData]])
