package reactapp.shared.business

import reactapp.shared.auth.Access
import reactapp.shared.common.grid.{ CfColumnType, GridColumn, GridDefinition }
import reactapp.shared.grid.Field
import sjsgrid.shared.common.Dependencies
import sjsgrid.shared.form.types.TextConf
import sjsgrid.shared.grid.column.ColumnType

import scala.collection.immutable

sealed trait BusinessContactsGrid extends GridColumn with Field.AlwaysFetch

trait BusinessContactsGridFromDb extends BusinessContactsGrid with Field.FromDb {
  override lazy val sqlSelectOpt = Some(underscoreName)
}

object BusinessContactsGrid extends GridDefinition.Editable.NoValidation[BusinessContactsGrid] {
  override def hasWriteAccess(implicit deps: Dependencies): Boolean = Access.BusinessPage.write.isAuthorized

  override def values: immutable.IndexedSeq[BusinessContactsGrid] = findValues

  // case object BusinessDetailGrid extends BusinessGrid with FilterModelType.Long

  case object Firstname extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(50)
  }

  case object Middlename extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(30)
  }

  case object Lastname extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(50)
  }

  case object Title extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(30)
  }

  case object Mobilenumber extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(30)
  }

  case object Fixnumber extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(30)
  }

  case object Faxnumber extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(30)
  }

  case object Email1 extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(50)
  }

  case object Email2 extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(50)
  }

  case object Active extends BusinessContactsGridFromDb with ColumnType.BooleanTrueOrNothing with Field.Editable {}

  case object Linkedinurl extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(500)
  }

  case object Website extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(500)
  }

  case object Comment extends BusinessContactsGridFromDb with ColumnType.Text with Field.Editable {
    override lazy val width: scala.Int = 8 * 12
    override lazy val defaultConf = TextConf(300)
  }

  trait _EntityColumn extends BusinessContactsGrid with CfColumnType._EntityColumn {
    override val dbPrefix: String = "person."
  }

  case object EntityCreatedBy extends _EntityColumn with CfColumnType.EntityCreatedBy

  case object EntityCreatedOn extends _EntityColumn with CfColumnType.EntityCreatedOn

  case object EntityUI extends _EntityColumn with CfColumnType.EntityUI

  case object EntityUpdatedBy extends _EntityColumn with CfColumnType.EntityUpdatedBy

  case object EntityUpdatedOn extends _EntityColumn with CfColumnType.EntityUpdatedOn

}
