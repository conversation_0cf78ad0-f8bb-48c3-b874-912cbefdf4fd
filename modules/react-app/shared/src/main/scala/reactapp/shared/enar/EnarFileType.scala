package reactapp.shared.enar

import scala.collection.immutable

/**
  * Created by Milan Satala
  * Date: 10/24/17
  * Time: 1:16 PM
  */
sealed abstract class EnarFileType(val code: Int, val name: String) extends enumeratum.EnumEntry

object EnarFileType extends enumeratum.Enum[EnarFileType] {

  case object Registration extends EnarFileType(5642, "Jelölés")

  case object Farrowing extends EnarFileType(5644, "Fialás")

  case object Exit extends EnarFileType(5646, "Kiesés")

  val values: immutable.IndexedSeq[EnarFileType] = findValues
}
