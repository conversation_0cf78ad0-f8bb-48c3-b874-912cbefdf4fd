package reactapp.shared.codetypes

import reactapp.shared.auth.{ Access, Authorization }
import reactapp.shared.grid.Field
import sjsgrid.shared.common.Dependencies

import scala.collection.immutable

object CostTypesGrid extends CodeTypesGridBase {
  def readAuthorization: Authorization = Access.CostTypesPage.read
  override def hasWriteAccess(implicit deps: Dependencies): Boolean = Access.CostTypesPage.write.isAuthorized

  override lazy val labelKey: String = "js.title.costtypes"

  case object Name extends NameColumn
  case object NameTranslation extends NameTranslationColumn

  override def values: immutable.IndexedSeq[CodeTypesGrid] = immutable.IndexedSeq(
    Mark,
    Code,
    Name,
    NameTranslation,
    Disabled,
  )
}
