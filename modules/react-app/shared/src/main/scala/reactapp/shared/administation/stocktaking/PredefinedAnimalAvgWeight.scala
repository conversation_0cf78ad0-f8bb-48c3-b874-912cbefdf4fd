package reactapp.shared.administation.stocktaking

import domainz.common.AnimalType

sealed trait PredefinedAnimalAvgWeight {
  def getAnimalWeightFromSettings(at: AnimalType): Option[BigDecimal]
}
object PredefinedAnimalAvgWeight {
  case object TurnedOff extends PredefinedAnimalAvgWeight {
    override def getAnimalWeightFromSettings(at: AnimalType): Option[BigDecimal] = None
  }

  case class AnimalAvgWeightValues(
    boarAvgWeightOpt: Option[Double],
    sowAvgWeightOpt: Option[Double],
    par0AvgWeightOpt: Option[Double],
    piglAvgWeightOpt: Option[Double],
  ) extends PredefinedAnimalAvgWeight {
    override def getAnimalWeightFromSettings(at: AnimalType): Option[BigDecimal] = {
      at match {
        case AnimalType.Boar            => boarAvgWeightOpt.map(BigDecimal(_))
        case AnimalType.Sow             => sowAvgWeightOpt.map(BigDecimal(_))
        case AnimalType.InseminatedGilt => par0AvgWeightOpt.map(BigDecimal(_))
        case AnimalType.Suckling        => piglAvgWeightOpt.map(BigDecimal(_))
        case _                          => None
      }
    }
  }
}
