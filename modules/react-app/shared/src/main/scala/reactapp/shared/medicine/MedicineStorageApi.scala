package reactapp.shared.medicine

import domainz.MedicineStorageItem
import org.scalactic.{ Every, Or }
import pushka.annotation.pushka
import sjsgrid.shared.grid.dto.{ DataGetterParams, _ }

@pushka
case class MedicineTransferStatistics(
  storageId: Long,
  before: Double,
  bought: Double,
  used: Double,
  correction: Double,
  stocktaking: Double,
  transferred: Double,
  current: Double,
  reserved: Double,
)

trait MedicineStorageApi {
  def medicineNames(): Seq[(Long, String)]
  def getTransferStatistics(
    prescriptionId: Long,
    filteringAndSorting: FilteringAndSorting[MedicineStorageTransferGrid],
  ): Seq[MedicineTransferStatistics]
  def listMedicineTransfers(
    prescriptionId: Long,
    params: DataGetterParams[MedicineStorageTransferGrid],
  ): Seq[(Option[Long], RowModel[MedicineStorageTransferGrid])]
  def countMedicineTransfers(prescriptionId: Long, filteringAndSorting: FilteringAndSorting[MedicineStorageTransferGrid]): Int
  def deleteMedicineTransfer(id: Long): Unit
  def updateMedicineTransfer(
    prescriptionId: Long,
    rowsMap: Seq[RowSaveDTO[MedicineStorageTransferGrid, Option[Long]]],
  ): Map[Int, Option[Long] Or Every[ServerProblem]]
  def getStocktakingData(prescriptionId: Long): Seq[(Long, String, Long)]
  def saveStocktakingData(prescriptionId: Long, storageValues: Seq[(Long, Long)])
  def medicineStorageNames(): Seq[MedicineStorageItem]
  def medicineStorageTransferTypes(): Seq[(String, String)]
  def medicineUnits(): Seq[(String, String)]
}
