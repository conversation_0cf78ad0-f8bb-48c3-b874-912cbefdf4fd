/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.holding.Holding;
import jooq.holding.Keys;
import jooq.holding.tables.records.IssueReasonTypeAllDeletedRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IssueReasonTypeAllDeleted extends TableImpl<IssueReasonTypeAllDeletedRecord> {

	private static final long serialVersionUID = -1313654238;

	/**
	 * The reference instance of <code>holding.issue_reason_type_all_deleted</code>
	 */
	public static final IssueReasonTypeAllDeleted ISSUE_REASON_TYPE_ALL_DELETED = new IssueReasonTypeAllDeleted();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<IssueReasonTypeAllDeletedRecord> getRecordType() {
		return IssueReasonTypeAllDeletedRecord.class;
	}

	/**
	 * The column <code>holding.issue_reason_type_all_deleted.code</code>.
	 */
	public final TableField<IssueReasonTypeAllDeletedRecord, String> CODE = createField("code", org.jooq.impl.SQLDataType.VARCHAR.length(15).nullable(false), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all_deleted.update_date</code>.
	 */
	public final TableField<IssueReasonTypeAllDeletedRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all_deleted.update_account_id</code>.
	 */
	public final TableField<IssueReasonTypeAllDeletedRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all_deleted.update_ui</code>.
	 */
	public final TableField<IssueReasonTypeAllDeletedRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>holding.issue_reason_type_all_deleted</code> table reference
	 */
	public IssueReasonTypeAllDeleted() {
		this("issue_reason_type_all_deleted", null);
	}

	/**
	 * Create an aliased <code>holding.issue_reason_type_all_deleted</code> table reference
	 */
	public IssueReasonTypeAllDeleted(String alias) {
		this(alias, ISSUE_REASON_TYPE_ALL_DELETED);
	}

	private IssueReasonTypeAllDeleted(String alias, Table<IssueReasonTypeAllDeletedRecord> aliased) {
		this(alias, aliased, null);
	}

	private IssueReasonTypeAllDeleted(String alias, Table<IssueReasonTypeAllDeletedRecord> aliased, Field<?>[] parameters) {
		super(alias, Holding.HOLDING, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<IssueReasonTypeAllDeletedRecord> getPrimaryKey() {
		return Keys.ISSUE_REASON_TYPE_ALL_DELETED_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<IssueReasonTypeAllDeletedRecord>> getKeys() {
		return Arrays.<UniqueKey<IssueReasonTypeAllDeletedRecord>>asList(Keys.ISSUE_REASON_TYPE_ALL_DELETED_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<IssueReasonTypeAllDeletedRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IssueReasonTypeAllDeleted as(String alias) {
		return new IssueReasonTypeAllDeleted(alias, this);
	}

	/**
	 * Rename this table
	 */
	public IssueReasonTypeAllDeleted rename(String name) {
		return new IssueReasonTypeAllDeleted(name, null);
	}
}
