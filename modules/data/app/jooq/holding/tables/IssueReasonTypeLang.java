/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.holding.Holding;
import jooq.holding.Keys;
import jooq.holding.tables.records.IssueReasonTypeLangRecord;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IssueReasonTypeLang extends TableImpl<IssueReasonTypeLangRecord> {

	private static final long serialVersionUID = 1191740771;

	/**
	 * The reference instance of <code>holding.issue_reason_type_lang</code>
	 */
	public static final IssueReasonTypeLang ISSUE_REASON_TYPE_LANG = new IssueReasonTypeLang();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<IssueReasonTypeLangRecord> getRecordType() {
		return IssueReasonTypeLangRecord.class;
	}

	/**
	 * The column <code>holding.issue_reason_type_lang.language_id</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, String> LANGUAGE_ID = createField("language_id", org.jooq.impl.SQLDataType.VARCHAR.length(5).nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_lang.code</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, String> CODE = createField("code", org.jooq.impl.SQLDataType.VARCHAR.length(15).nullable(false), this, "");

	/**
	 * The column <code>holding.issue_reason_type_lang.name</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, String> NAME = createField("name", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "");

	/**
	 * The column <code>holding.issue_reason_type_lang.description</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, String> DESCRIPTION = createField("description", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "");

	/**
	 * The column <code>holding.issue_reason_type_lang.create_date</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_lang.update_date</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_lang.create_account_id</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, Long> CREATE_ACCOUNT_ID = createField("create_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_lang.update_account_id</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_lang.create_ui</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, String> CREATE_UI = createField("create_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_lang.update_ui</code>.
	 */
	public final TableField<IssueReasonTypeLangRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>holding.issue_reason_type_lang</code> table reference
	 */
	public IssueReasonTypeLang() {
		this("issue_reason_type_lang", null);
	}

	/**
	 * Create an aliased <code>holding.issue_reason_type_lang</code> table reference
	 */
	public IssueReasonTypeLang(String alias) {
		this(alias, ISSUE_REASON_TYPE_LANG);
	}

	private IssueReasonTypeLang(String alias, Table<IssueReasonTypeLangRecord> aliased) {
		this(alias, aliased, null);
	}

	private IssueReasonTypeLang(String alias, Table<IssueReasonTypeLangRecord> aliased, Field<?>[] parameters) {
		super(alias, Holding.HOLDING, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<IssueReasonTypeLangRecord> getPrimaryKey() {
		return Keys.ISSUE_REASON_TYPE_LANG_PK;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<IssueReasonTypeLangRecord>> getKeys() {
		return Arrays.<UniqueKey<IssueReasonTypeLangRecord>>asList(Keys.ISSUE_REASON_TYPE_LANG_PK);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<ForeignKey<IssueReasonTypeLangRecord, ?>> getReferences() {
		return Arrays.<ForeignKey<IssueReasonTypeLangRecord, ?>>asList(Keys.ISSUE_REASON_TYPE_LANG__ISSUE_REASON_TYPE_LANG_CODE_FK);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<IssueReasonTypeLangRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IssueReasonTypeLang as(String alias) {
		return new IssueReasonTypeLang(alias, this);
	}

	/**
	 * Rename this table
	 */
	public IssueReasonTypeLang rename(String name) {
		return new IssueReasonTypeLang(name, null);
	}
}
