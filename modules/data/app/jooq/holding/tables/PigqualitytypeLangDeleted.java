/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.holding.Holding;
import jooq.holding.Keys;
import jooq.holding.tables.records.PigqualitytypeLangDeletedRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PigqualitytypeLangDeleted extends TableImpl<PigqualitytypeLangDeletedRecord> {

	private static final long serialVersionUID = -560345202;

	/**
	 * The reference instance of <code>holding.pigqualitytype_lang_deleted</code>
	 */
	public static final PigqualitytypeLangDeleted PIGQUALITYTYPE_LANG_DELETED = new PigqualitytypeLangDeleted();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<PigqualitytypeLangDeletedRecord> getRecordType() {
		return PigqualitytypeLangDeletedRecord.class;
	}

	/**
	 * The column <code>holding.pigqualitytype_lang_deleted.code</code>.
	 */
	public final TableField<PigqualitytypeLangDeletedRecord, String> CODE = createField("code", org.jooq.impl.SQLDataType.VARCHAR.length(15).nullable(false), this, "");

	/**
	 * The column <code>holding.pigqualitytype_lang_deleted.language_id</code>.
	 */
	public final TableField<PigqualitytypeLangDeletedRecord, String> LANGUAGE_ID = createField("language_id", org.jooq.impl.SQLDataType.VARCHAR.length(5).nullable(false), this, "");

	/**
	 * The column <code>holding.pigqualitytype_lang_deleted.update_date</code>.
	 */
	public final TableField<PigqualitytypeLangDeletedRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false), this, "");

	/**
	 * The column <code>holding.pigqualitytype_lang_deleted.update_account_id</code>.
	 */
	public final TableField<PigqualitytypeLangDeletedRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.pigqualitytype_lang_deleted.update_ui</code>.
	 */
	public final TableField<PigqualitytypeLangDeletedRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>holding.pigqualitytype_lang_deleted</code> table reference
	 */
	public PigqualitytypeLangDeleted() {
		this("pigqualitytype_lang_deleted", null);
	}

	/**
	 * Create an aliased <code>holding.pigqualitytype_lang_deleted</code> table reference
	 */
	public PigqualitytypeLangDeleted(String alias) {
		this(alias, PIGQUALITYTYPE_LANG_DELETED);
	}

	private PigqualitytypeLangDeleted(String alias, Table<PigqualitytypeLangDeletedRecord> aliased) {
		this(alias, aliased, null);
	}

	private PigqualitytypeLangDeleted(String alias, Table<PigqualitytypeLangDeletedRecord> aliased, Field<?>[] parameters) {
		super(alias, Holding.HOLDING, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<PigqualitytypeLangDeletedRecord> getPrimaryKey() {
		return Keys.PIGQUALITYTYPE_LANG_DELETED_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<PigqualitytypeLangDeletedRecord>> getKeys() {
		return Arrays.<UniqueKey<PigqualitytypeLangDeletedRecord>>asList(Keys.PIGQUALITYTYPE_LANG_DELETED_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<PigqualitytypeLangDeletedRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitytypeLangDeleted as(String alias) {
		return new PigqualitytypeLangDeleted(alias, this);
	}

	/**
	 * Rename this table
	 */
	public PigqualitytypeLangDeleted rename(String name) {
		return new PigqualitytypeLangDeleted(name, null);
	}
}
