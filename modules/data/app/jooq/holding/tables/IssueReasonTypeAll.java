/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.holding.Holding;
import jooq.holding.Keys;
import jooq.holding.tables.records.IssueReasonTypeAllRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IssueReasonTypeAll extends TableImpl<IssueReasonTypeAllRecord> {

	private static final long serialVersionUID = 503754151;

	/**
	 * The reference instance of <code>holding.issue_reason_type_all</code>
	 */
	public static final IssueReasonTypeAll ISSUE_REASON_TYPE_ALL = new IssueReasonTypeAll();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<IssueReasonTypeAllRecord> getRecordType() {
		return IssueReasonTypeAllRecord.class;
	}

	/**
	 * The column <code>holding.issue_reason_type_all.code</code>.
	 */
	public final TableField<IssueReasonTypeAllRecord, String> CODE = createField("code", org.jooq.impl.SQLDataType.VARCHAR.length(15).nullable(false), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all.name</code>.
	 */
	public final TableField<IssueReasonTypeAllRecord, String> NAME = createField("name", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all.description</code>.
	 */
	public final TableField<IssueReasonTypeAllRecord, String> DESCRIPTION = createField("description", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all.create_date</code>.
	 */
	public final TableField<IssueReasonTypeAllRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all.update_date</code>.
	 */
	public final TableField<IssueReasonTypeAllRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all.create_account_id</code>.
	 */
	public final TableField<IssueReasonTypeAllRecord, Long> CREATE_ACCOUNT_ID = createField("create_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all.update_account_id</code>.
	 */
	public final TableField<IssueReasonTypeAllRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all.create_ui</code>.
	 */
	public final TableField<IssueReasonTypeAllRecord, String> CREATE_UI = createField("create_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * The column <code>holding.issue_reason_type_all.update_ui</code>.
	 */
	public final TableField<IssueReasonTypeAllRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>holding.issue_reason_type_all</code> table reference
	 */
	public IssueReasonTypeAll() {
		this("issue_reason_type_all", null);
	}

	/**
	 * Create an aliased <code>holding.issue_reason_type_all</code> table reference
	 */
	public IssueReasonTypeAll(String alias) {
		this(alias, ISSUE_REASON_TYPE_ALL);
	}

	private IssueReasonTypeAll(String alias, Table<IssueReasonTypeAllRecord> aliased) {
		this(alias, aliased, null);
	}

	private IssueReasonTypeAll(String alias, Table<IssueReasonTypeAllRecord> aliased, Field<?>[] parameters) {
		super(alias, Holding.HOLDING, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<IssueReasonTypeAllRecord> getPrimaryKey() {
		return Keys.ISSUE_REASON_TYPE_ALL_PK;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<IssueReasonTypeAllRecord>> getKeys() {
		return Arrays.<UniqueKey<IssueReasonTypeAllRecord>>asList(Keys.ISSUE_REASON_TYPE_ALL_PK);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<IssueReasonTypeAllRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IssueReasonTypeAll as(String alias) {
		return new IssueReasonTypeAll(alias, this);
	}

	/**
	 * Rename this table
	 */
	public IssueReasonTypeAll rename(String name) {
		return new IssueReasonTypeAll(name, null);
	}
}
