/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables.records;


import java.sql.Date;

import javax.annotation.Generated;

import jooq.holding.tables.PbbBrazilUpload;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PbbBrazilUploadRecord extends UpdatableRecordImpl<PbbBrazilUploadRecord> implements Record5<Integer, String, Date, Integer, String> {

	private static final long serialVersionUID = 990739087;

	/**
	 * Setter for <code>holding.pbb_brazil_upload.id</code>.
	 */
	public void setId(Integer value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>holding.pbb_brazil_upload.id</code>.
	 */
	public Integer getId() {
		return (Integer) getValue(0);
	}

	/**
	 * Setter for <code>holding.pbb_brazil_upload.documentnumber</code>.
	 */
	public void setDocumentnumber(String value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>holding.pbb_brazil_upload.documentnumber</code>.
	 */
	public String getDocumentnumber() {
		return (String) getValue(1);
	}

	/**
	 * Setter for <code>holding.pbb_brazil_upload.documentdate</code>.
	 */
	public void setDocumentdate(Date value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>holding.pbb_brazil_upload.documentdate</code>.
	 */
	public Date getDocumentdate() {
		return (Date) getValue(2);
	}

	/**
	 * Setter for <code>holding.pbb_brazil_upload.numberofanimals</code>.
	 */
	public void setNumberofanimals(Integer value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>holding.pbb_brazil_upload.numberofanimals</code>.
	 */
	public Integer getNumberofanimals() {
		return (Integer) getValue(3);
	}

	/**
	 * Setter for <code>holding.pbb_brazil_upload.farmname</code>.
	 */
	public void setFarmname(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>holding.pbb_brazil_upload.farmname</code>.
	 */
	public String getFarmname() {
		return (String) getValue(4);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Integer> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record5 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row5<Integer, String, Date, Integer, String> fieldsRow() {
		return (Row5) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row5<Integer, String, Date, Integer, String> valuesRow() {
		return (Row5) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field1() {
		return PbbBrazilUpload.PBB_BRAZIL_UPLOAD.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field2() {
		return PbbBrazilUpload.PBB_BRAZIL_UPLOAD.DOCUMENTNUMBER;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Date> field3() {
		return PbbBrazilUpload.PBB_BRAZIL_UPLOAD.DOCUMENTDATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field4() {
		return PbbBrazilUpload.PBB_BRAZIL_UPLOAD.NUMBEROFANIMALS;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return PbbBrazilUpload.PBB_BRAZIL_UPLOAD.FARMNAME;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value1() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value2() {
		return getDocumentnumber();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Date value3() {
		return getDocumentdate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value4() {
		return getNumberofanimals();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getFarmname();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PbbBrazilUploadRecord value1(Integer value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PbbBrazilUploadRecord value2(String value) {
		setDocumentnumber(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PbbBrazilUploadRecord value3(Date value) {
		setDocumentdate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PbbBrazilUploadRecord value4(Integer value) {
		setNumberofanimals(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PbbBrazilUploadRecord value5(String value) {
		setFarmname(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PbbBrazilUploadRecord values(Integer value1, String value2, Date value3, Integer value4, String value5) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached PbbBrazilUploadRecord
	 */
	public PbbBrazilUploadRecord() {
		super(PbbBrazilUpload.PBB_BRAZIL_UPLOAD);
	}

	/**
	 * Create a detached, initialised PbbBrazilUploadRecord
	 */
	public PbbBrazilUploadRecord(Integer id, String documentnumber, Date documentdate, Integer numberofanimals, String farmname) {
		super(PbbBrazilUpload.PBB_BRAZIL_UPLOAD);

		setValue(0, id);
		setValue(1, documentnumber);
		setValue(2, documentdate);
		setValue(3, numberofanimals);
		setValue(4, farmname);
	}
}
