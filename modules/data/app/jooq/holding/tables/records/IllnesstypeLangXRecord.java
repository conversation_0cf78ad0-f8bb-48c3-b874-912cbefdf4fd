/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.holding.tables.IllnesstypeLangX;

import org.jooq.Field;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IllnesstypeLangXRecord extends TableRecordImpl<IllnesstypeLangXRecord> implements Record8<Timestamp, Timestamp, Long, Long, String, String, String, String> {

	private static final long serialVersionUID = -1878322093;

	/**
	 * Setter for <code>holding.illnesstype_lang_x.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>holding.illnesstype_lang_x.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(0);
	}

	/**
	 * Setter for <code>holding.illnesstype_lang_x.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>holding.illnesstype_lang_x.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>holding.illnesstype_lang_x.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>holding.illnesstype_lang_x.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>holding.illnesstype_lang_x.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>holding.illnesstype_lang_x.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>holding.illnesstype_lang_x.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>holding.illnesstype_lang_x.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>holding.illnesstype_lang_x.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>holding.illnesstype_lang_x.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>holding.illnesstype_lang_x.code</code>.
	 */
	public void setCode(String value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>holding.illnesstype_lang_x.code</code>.
	 */
	public String getCode() {
		return (String) getValue(6);
	}

	/**
	 * Setter for <code>holding.illnesstype_lang_x.description</code>.
	 */
	public void setDescription(String value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>holding.illnesstype_lang_x.description</code>.
	 */
	public String getDescription() {
		return (String) getValue(7);
	}

	// -------------------------------------------------------------------------
	// Record8 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row8<Timestamp, Timestamp, Long, Long, String, String, String, String> fieldsRow() {
		return (Row8) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row8<Timestamp, Timestamp, Long, Long, String, String, String, String> valuesRow() {
		return (Row8) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field1() {
		return IllnesstypeLangX.ILLNESSTYPE_LANG_X.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return IllnesstypeLangX.ILLNESSTYPE_LANG_X.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return IllnesstypeLangX.ILLNESSTYPE_LANG_X.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return IllnesstypeLangX.ILLNESSTYPE_LANG_X.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return IllnesstypeLangX.ILLNESSTYPE_LANG_X.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return IllnesstypeLangX.ILLNESSTYPE_LANG_X.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field7() {
		return IllnesstypeLangX.ILLNESSTYPE_LANG_X.CODE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field8() {
		return IllnesstypeLangX.ILLNESSTYPE_LANG_X.DESCRIPTION;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value1() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value7() {
		return getCode();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value8() {
		return getDescription();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IllnesstypeLangXRecord value1(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IllnesstypeLangXRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IllnesstypeLangXRecord value3(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IllnesstypeLangXRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IllnesstypeLangXRecord value5(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IllnesstypeLangXRecord value6(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IllnesstypeLangXRecord value7(String value) {
		setCode(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IllnesstypeLangXRecord value8(String value) {
		setDescription(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IllnesstypeLangXRecord values(Timestamp value1, Timestamp value2, Long value3, Long value4, String value5, String value6, String value7, String value8) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached IllnesstypeLangXRecord
	 */
	public IllnesstypeLangXRecord() {
		super(IllnesstypeLangX.ILLNESSTYPE_LANG_X);
	}

	/**
	 * Create a detached, initialised IllnesstypeLangXRecord
	 */
	public IllnesstypeLangXRecord(Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, String code, String description) {
		super(IllnesstypeLangX.ILLNESSTYPE_LANG_X);

		setValue(0, createDate);
		setValue(1, updateDate);
		setValue(2, createAccountId);
		setValue(3, updateAccountId);
		setValue(4, createUi);
		setValue(5, updateUi);
		setValue(6, code);
		setValue(7, description);
	}
}
