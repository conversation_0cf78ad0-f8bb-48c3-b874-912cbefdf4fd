/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.holding.tables.IssueReasonTypeLangDeleted;

import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IssueReasonTypeLangDeletedRecord extends UpdatableRecordImpl<IssueReasonTypeLangDeletedRecord> implements Record5<String, String, Timestamp, Long, String> {

	private static final long serialVersionUID = -1669097940;

	/**
	 * Setter for <code>holding.issue_reason_type_lang_deleted.code</code>.
	 */
	public void setCode(String value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>holding.issue_reason_type_lang_deleted.code</code>.
	 */
	public String getCode() {
		return (String) getValue(0);
	}

	/**
	 * Setter for <code>holding.issue_reason_type_lang_deleted.language_id</code>.
	 */
	public void setLanguageId(String value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>holding.issue_reason_type_lang_deleted.language_id</code>.
	 */
	public String getLanguageId() {
		return (String) getValue(1);
	}

	/**
	 * Setter for <code>holding.issue_reason_type_lang_deleted.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>holding.issue_reason_type_lang_deleted.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(2);
	}

	/**
	 * Setter for <code>holding.issue_reason_type_lang_deleted.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>holding.issue_reason_type_lang_deleted.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>holding.issue_reason_type_lang_deleted.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>holding.issue_reason_type_lang_deleted.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(4);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record2<String, String> key() {
		return (Record2) super.key();
	}

	// -------------------------------------------------------------------------
	// Record5 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row5<String, String, Timestamp, Long, String> fieldsRow() {
		return (Row5) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row5<String, String, Timestamp, Long, String> valuesRow() {
		return (Row5) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field1() {
		return IssueReasonTypeLangDeleted.ISSUE_REASON_TYPE_LANG_DELETED.CODE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field2() {
		return IssueReasonTypeLangDeleted.ISSUE_REASON_TYPE_LANG_DELETED.LANGUAGE_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field3() {
		return IssueReasonTypeLangDeleted.ISSUE_REASON_TYPE_LANG_DELETED.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return IssueReasonTypeLangDeleted.ISSUE_REASON_TYPE_LANG_DELETED.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return IssueReasonTypeLangDeleted.ISSUE_REASON_TYPE_LANG_DELETED.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value1() {
		return getCode();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value2() {
		return getLanguageId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value3() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IssueReasonTypeLangDeletedRecord value1(String value) {
		setCode(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IssueReasonTypeLangDeletedRecord value2(String value) {
		setLanguageId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IssueReasonTypeLangDeletedRecord value3(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IssueReasonTypeLangDeletedRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IssueReasonTypeLangDeletedRecord value5(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IssueReasonTypeLangDeletedRecord values(String value1, String value2, Timestamp value3, Long value4, String value5) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached IssueReasonTypeLangDeletedRecord
	 */
	public IssueReasonTypeLangDeletedRecord() {
		super(IssueReasonTypeLangDeleted.ISSUE_REASON_TYPE_LANG_DELETED);
	}

	/**
	 * Create a detached, initialised IssueReasonTypeLangDeletedRecord
	 */
	public IssueReasonTypeLangDeletedRecord(String code, String languageId, Timestamp updateDate, Long updateAccountId, String updateUi) {
		super(IssueReasonTypeLangDeleted.ISSUE_REASON_TYPE_LANG_DELETED);

		setValue(0, code);
		setValue(1, languageId);
		setValue(2, updateDate);
		setValue(3, updateAccountId);
		setValue(4, updateUi);
	}
}
