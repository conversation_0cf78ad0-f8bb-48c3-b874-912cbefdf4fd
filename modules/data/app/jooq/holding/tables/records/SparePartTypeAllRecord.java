/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables.records;


import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.holding.tables.SparePartTypeAll;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SparePartTypeAllRecord extends UpdatableRecordImpl<SparePartTypeAllRecord> implements Record12<String, String, String, String, BigDecimal, Short, Timestamp, Timestamp, Long, Long, String, String> {

	private static final long serialVersionUID = -338811739;

	/**
	 * Setter for <code>holding.spare_part_type_all.code</code>.
	 */
	public void setCode(String value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.code</code>.
	 */
	public String getCode() {
		return (String) getValue(0);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.name</code>.
	 */
	public void setName(String value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.name</code>.
	 */
	public String getName() {
		return (String) getValue(1);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.category</code>.
	 */
	public void setCategory(String value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.category</code>.
	 */
	public String getCategory() {
		return (String) getValue(2);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.packaging</code>.
	 */
	public void setPackaging(String value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.packaging</code>.
	 */
	public String getPackaging() {
		return (String) getValue(3);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.package_size</code>.
	 */
	public void setPackageSize(BigDecimal value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.package_size</code>.
	 */
	public BigDecimal getPackageSize() {
		return (BigDecimal) getValue(4);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.serial_numbers</code>.
	 */
	public void setSerialNumbers(Short value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.serial_numbers</code>.
	 */
	public Short getSerialNumbers() {
		return (Short) getValue(5);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(6);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(7);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(8);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(9);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(10, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(10);
	}

	/**
	 * Setter for <code>holding.spare_part_type_all.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(11, value);
	}

	/**
	 * Getter for <code>holding.spare_part_type_all.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(11);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<String> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record12 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row12<String, String, String, String, BigDecimal, Short, Timestamp, Timestamp, Long, Long, String, String> fieldsRow() {
		return (Row12) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row12<String, String, String, String, BigDecimal, Short, Timestamp, Timestamp, Long, Long, String, String> valuesRow() {
		return (Row12) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field1() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.CODE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field2() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.NAME;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field3() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.CATEGORY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field4() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.PACKAGING;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<BigDecimal> field5() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.PACKAGE_SIZE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Short> field6() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.SERIAL_NUMBERS;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field7() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field8() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field9() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field10() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field11() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field12() {
		return SparePartTypeAll.SPARE_PART_TYPE_ALL.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value1() {
		return getCode();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value2() {
		return getName();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value3() {
		return getCategory();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value4() {
		return getPackaging();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BigDecimal value5() {
		return getPackageSize();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Short value6() {
		return getSerialNumbers();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value7() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value8() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value9() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value10() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value11() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value12() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value1(String value) {
		setCode(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value2(String value) {
		setName(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value3(String value) {
		setCategory(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value4(String value) {
		setPackaging(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value5(BigDecimal value) {
		setPackageSize(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value6(Short value) {
		setSerialNumbers(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value7(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value8(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value9(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value10(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value11(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord value12(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartTypeAllRecord values(String value1, String value2, String value3, String value4, BigDecimal value5, Short value6, Timestamp value7, Timestamp value8, Long value9, Long value10, String value11, String value12) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		value11(value11);
		value12(value12);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached SparePartTypeAllRecord
	 */
	public SparePartTypeAllRecord() {
		super(SparePartTypeAll.SPARE_PART_TYPE_ALL);
	}

	/**
	 * Create a detached, initialised SparePartTypeAllRecord
	 */
	public SparePartTypeAllRecord(String code, String name, String category, String packaging, BigDecimal packageSize, Short serialNumbers, Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi) {
		super(SparePartTypeAll.SPARE_PART_TYPE_ALL);

		setValue(0, code);
		setValue(1, name);
		setValue(2, category);
		setValue(3, packaging);
		setValue(4, packageSize);
		setValue(5, serialNumbers);
		setValue(6, createDate);
		setValue(7, updateDate);
		setValue(8, createAccountId);
		setValue(9, updateAccountId);
		setValue(10, createUi);
		setValue(11, updateUi);
	}
}
