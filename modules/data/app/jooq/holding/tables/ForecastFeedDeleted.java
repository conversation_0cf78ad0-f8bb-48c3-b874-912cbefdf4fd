/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.holding.Holding;
import jooq.holding.Keys;
import jooq.holding.tables.records.ForecastFeedDeletedRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ForecastFeedDeleted extends TableImpl<ForecastFeedDeletedRecord> {

	private static final long serialVersionUID = 250712244;

	/**
	 * The reference instance of <code>holding.forecast_feed_deleted</code>
	 */
	public static final ForecastFeedDeleted FORECAST_FEED_DELETED = new ForecastFeedDeleted();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<ForecastFeedDeletedRecord> getRecordType() {
		return ForecastFeedDeletedRecord.class;
	}

	/**
	 * The column <code>holding.forecast_feed_deleted.forecast_data_id</code>.
	 */
	public final TableField<ForecastFeedDeletedRecord, Long> FORECAST_DATA_ID = createField("forecast_data_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>holding.forecast_feed_deleted.feed_name</code>.
	 */
	public final TableField<ForecastFeedDeletedRecord, String> FEED_NAME = createField("feed_name", org.jooq.impl.SQLDataType.VARCHAR.length(30).nullable(false), this, "");

	/**
	 * The column <code>holding.forecast_feed_deleted.update_date</code>.
	 */
	public final TableField<ForecastFeedDeletedRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.forecast_feed_deleted.update_account_id</code>.
	 */
	public final TableField<ForecastFeedDeletedRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.forecast_feed_deleted.update_ui</code>.
	 */
	public final TableField<ForecastFeedDeletedRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>holding.forecast_feed_deleted</code> table reference
	 */
	public ForecastFeedDeleted() {
		this("forecast_feed_deleted", null);
	}

	/**
	 * Create an aliased <code>holding.forecast_feed_deleted</code> table reference
	 */
	public ForecastFeedDeleted(String alias) {
		this(alias, FORECAST_FEED_DELETED);
	}

	private ForecastFeedDeleted(String alias, Table<ForecastFeedDeletedRecord> aliased) {
		this(alias, aliased, null);
	}

	private ForecastFeedDeleted(String alias, Table<ForecastFeedDeletedRecord> aliased, Field<?>[] parameters) {
		super(alias, Holding.HOLDING, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<ForecastFeedDeletedRecord> getPrimaryKey() {
		return Keys.FORECAST_DATA_ID_FEED_NAME_PK;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<ForecastFeedDeletedRecord>> getKeys() {
		return Arrays.<UniqueKey<ForecastFeedDeletedRecord>>asList(Keys.FORECAST_DATA_ID_FEED_NAME_PK);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<ForecastFeedDeletedRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ForecastFeedDeleted as(String alias) {
		return new ForecastFeedDeleted(alias, this);
	}

	/**
	 * Rename this table
	 */
	public ForecastFeedDeleted rename(String name) {
		return new ForecastFeedDeleted(name, null);
	}
}
