/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.DangenMessageRecord;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DangenMessage extends TableImpl<DangenMessageRecord> {

	private static final long serialVersionUID = 295136962;

	/**
	 * The reference instance of <code>farm.dangen_message</code>
	 */
	public static final DangenMessage DANGEN_MESSAGE = new DangenMessage();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<DangenMessageRecord> getRecordType() {
		return DangenMessageRecord.class;
	}

	/**
	 * The column <code>farm.dangen_message.id</code>.
	 */
	public final TableField<DangenMessageRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.dangen_message.dangen_id</code>.
	 */
	public final TableField<DangenMessageRecord, String> DANGEN_ID = createField("dangen_id", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "");

	/**
	 * The column <code>farm.dangen_message.state</code>.
	 */
	public final TableField<DangenMessageRecord, String> STATE = createField("state", org.jooq.impl.SQLDataType.CHAR.length(1).nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.dangen_message.sent</code>.
	 */
	public final TableField<DangenMessageRecord, Timestamp> SENT = createField("sent", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.dangen_message.failure</code>.
	 */
	public final TableField<DangenMessageRecord, String> FAILURE = createField("failure", org.jooq.impl.SQLDataType.CLOB, this, "");

	/**
	 * Create a <code>farm.dangen_message</code> table reference
	 */
	public DangenMessage() {
		this("dangen_message", null);
	}

	/**
	 * Create an aliased <code>farm.dangen_message</code> table reference
	 */
	public DangenMessage(String alias) {
		this(alias, DANGEN_MESSAGE);
	}

	private DangenMessage(String alias, Table<DangenMessageRecord> aliased) {
		this(alias, aliased, null);
	}

	private DangenMessage(String alias, Table<DangenMessageRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Identity<DangenMessageRecord, Long> getIdentity() {
		return Keys.IDENTITY_DANGEN_MESSAGE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<DangenMessageRecord> getPrimaryKey() {
		return Keys.DANGEN_MESSAGE_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<DangenMessageRecord>> getKeys() {
		return Arrays.<UniqueKey<DangenMessageRecord>>asList(Keys.DANGEN_MESSAGE_PKEY, Keys.DANGEN_MESSAGE_DANGEN_ID_KEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DangenMessage as(String alias) {
		return new DangenMessage(alias, this);
	}

	/**
	 * Rename this table
	 */
	public DangenMessage rename(String name) {
		return new DangenMessage(name, null);
	}
}
