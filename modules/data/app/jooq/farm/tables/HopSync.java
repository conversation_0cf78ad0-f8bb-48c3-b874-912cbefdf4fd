/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.HopSyncRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class HopSync extends TableImpl<HopSyncRecord> {

	private static final long serialVersionUID = 1812516655;

	/**
	 * The reference instance of <code>farm.hop_sync</code>
	 */
	public static final HopSync HOP_SYNC = new HopSync();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<HopSyncRecord> getRecordType() {
		return HopSyncRecord.class;
	}

	/**
	 * The column <code>farm.hop_sync.id</code>.
	 */
	public final TableField<HopSyncRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>farm.hop_sync.traceability_id</code>.
	 */
	public final TableField<HopSyncRecord, String> TRACEABILITY_ID = createField("traceability_id", org.jooq.impl.SQLDataType.CLOB.nullable(false), this, "");

	/**
	 * The column <code>farm.hop_sync.sync_date</code>.
	 */
	public final TableField<HopSyncRecord, Timestamp> SYNC_DATE = createField("sync_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.hop_sync.sync_status</code>.
	 */
	public final TableField<HopSyncRecord, String> SYNC_STATUS = createField("sync_status", org.jooq.impl.SQLDataType.CLOB, this, "");

	/**
	 * The column <code>farm.hop_sync.parent_id</code>.
	 */
	public final TableField<HopSyncRecord, Long> PARENT_ID = createField("parent_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * Create a <code>farm.hop_sync</code> table reference
	 */
	public HopSync() {
		this("hop_sync", null);
	}

	/**
	 * Create an aliased <code>farm.hop_sync</code> table reference
	 */
	public HopSync(String alias) {
		this(alias, HOP_SYNC);
	}

	private HopSync(String alias, Table<HopSyncRecord> aliased) {
		this(alias, aliased, null);
	}

	private HopSync(String alias, Table<HopSyncRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<HopSyncRecord> getPrimaryKey() {
		return Keys.HOP_SYNC_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<HopSyncRecord>> getKeys() {
		return Arrays.<UniqueKey<HopSyncRecord>>asList(Keys.HOP_SYNC_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public HopSync as(String alias) {
		return new HopSync(alias, this);
	}

	/**
	 * Rename this table
	 */
	public HopSync rename(String name) {
		return new HopSync(name, null);
	}
}
