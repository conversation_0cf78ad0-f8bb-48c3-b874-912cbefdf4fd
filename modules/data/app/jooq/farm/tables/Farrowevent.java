/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.FarroweventRecord;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * stores all the events during the farrowing phase
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Farrowevent extends TableImpl<FarroweventRecord> {

	private static final long serialVersionUID = -1598321479;

	/**
	 * The reference instance of <code>farm.farrowevent</code>
	 */
	public static final Farrowevent FARROWEVENT = new Farrowevent();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<FarroweventRecord> getRecordType() {
		return FarroweventRecord.class;
	}

	/**
	 * The column <code>farm.farrowevent.create_date</code>.
	 */
	public final TableField<FarroweventRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farrowevent.update_date</code>.
	 */
	public final TableField<FarroweventRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farrowevent.create_account_id</code>.
	 */
	public final TableField<FarroweventRecord, Long> CREATE_ACCOUNT_ID = createField("create_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farrowevent.update_account_id</code>.
	 */
	public final TableField<FarroweventRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farrowevent.create_ui</code>.
	 */
	public final TableField<FarroweventRecord, String> CREATE_UI = createField("create_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * The column <code>farm.farrowevent.update_ui</code>.
	 */
	public final TableField<FarroweventRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * The column <code>farm.farrowevent.id</code>.
	 */
	public final TableField<FarroweventRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farrowevent.serving_id</code>.
	 */
	public final TableField<FarroweventRecord, Long> SERVING_ID = createField("serving_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>farm.farrowevent.liveborn</code>. number of live born piglets
	 */
	public final TableField<FarroweventRecord, Integer> LIVEBORN = createField("liveborn", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "number of live born piglets");

	/**
	 * The column <code>farm.farrowevent.stillborn</code>. number of stillborn piglets
	 */
	public final TableField<FarroweventRecord, Integer> STILLBORN = createField("stillborn", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "number of stillborn piglets");

	/**
	 * The column <code>farm.farrowevent.mummificated</code>. number of mummificated piglets
	 */
	public final TableField<FarroweventRecord, Integer> MUMMIFICATED = createField("mummificated", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "number of mummificated piglets");

	/**
	 * The column <code>farm.farrowevent.femalepiglets</code>. number of female piglets
	 */
	public final TableField<FarroweventRecord, Integer> FEMALEPIGLETS = createField("femalepiglets", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "number of female piglets");

	/**
	 * The column <code>farm.farrowevent.comment</code>.
	 */
	public final TableField<FarroweventRecord, String> COMMENT = createField("comment", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "");

	/**
	 * The column <code>farm.farrowevent.actor_id</code>.
	 */
	public final TableField<FarroweventRecord, Long> ACTOR_ID = createField("actor_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.farrowevent.actor_date</code>.
	 */
	public final TableField<FarroweventRecord, Timestamp> ACTOR_DATE = createField("actor_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.farrowevent.state</code>.
	 */
	public final TableField<FarroweventRecord, Short> STATE = createField("state", org.jooq.impl.SQLDataType.SMALLINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farrowevent.weakborn</code>.
	 */
	public final TableField<FarroweventRecord, Integer> WEAKBORN = createField("weakborn", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "");

	/**
	 * The column <code>farm.farrowevent.issue_helped</code>.
	 */
	public final TableField<FarroweventRecord, Boolean> ISSUE_HELPED = createField("issue_helped", org.jooq.impl.SQLDataType.BOOLEAN, this, "");

    public final TableField<FarroweventRecord, Integer> SB_PREPARTUM = createField("sb_prepartum", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "");
    public final TableField<FarroweventRecord, Integer> SB_INTRAPARTUM = createField("sb_intrapartum", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "");
    public final TableField<FarroweventRecord, Integer> SB_PARTIAL_INTRAPARTUM = createField("sb_partial_intrapartum", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "");
    public final TableField<FarroweventRecord, Integer> SB_LATE_GESTATIONAL = createField("sb_late_gestational", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "");
    public final TableField<FarroweventRecord, Integer> SB_ABNORMAL = createField("sb_abnormal", org.jooq.impl.SQLDataType.INTEGER.defaulted(true), this, "");

	/**
	 * Create a <code>farm.farrowevent</code> table reference
	 */
	public Farrowevent() {
		this("farrowevent", null);
	}

	/**
	 * Create an aliased <code>farm.farrowevent</code> table reference
	 */
	public Farrowevent(String alias) {
		this(alias, FARROWEVENT);
	}

	private Farrowevent(String alias, Table<FarroweventRecord> aliased) {
		this(alias, aliased, null);
	}

	private Farrowevent(String alias, Table<FarroweventRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "stores all the events during the farrowing phase");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Identity<FarroweventRecord, Long> getIdentity() {
		return Keys.IDENTITY_FARROWEVENT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<FarroweventRecord> getPrimaryKey() {
		return Keys.FARROWEVENT_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<FarroweventRecord>> getKeys() {
		return Arrays.<UniqueKey<FarroweventRecord>>asList(Keys.FARROWEVENT_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<ForeignKey<FarroweventRecord, ?>> getReferences() {
		return Arrays.<ForeignKey<FarroweventRecord, ?>>asList(Keys.FARROWEVENT__FARROWEVENT_SERVING_ID_FKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<FarroweventRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Farrowevent as(String alias) {
		return new Farrowevent(alias, this);
	}

	/**
	 * Rename this table
	 */
	public Farrowevent rename(String name) {
		return new Farrowevent(name, null);
	}
}
