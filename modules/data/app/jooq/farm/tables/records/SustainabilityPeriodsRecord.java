/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Date;
import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.SustainabilityPeriods;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SustainabilityPeriodsRecord extends UpdatableRecordImpl<SustainabilityPeriodsRecord> implements Record10<Timestamp, Timestamp, Long, Long, String, String, Long, Date, Date, Date> {

	private static final long serialVersionUID = 1295558677;

	/**
	 * Setter for <code>farm.sustainability_periods.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(0);
	}

	/**
	 * Setter for <code>farm.sustainability_periods.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.sustainability_periods.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.sustainability_periods.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>farm.sustainability_periods.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.sustainability_periods.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>farm.sustainability_periods.id</code>.
	 */
	public void setId(Long value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.id</code>.
	 */
	public Long getId() {
		return (Long) getValue(6);
	}

	/**
	 * Setter for <code>farm.sustainability_periods.calculation_date</code>.
	 */
	public void setCalculationDate(Date value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.calculation_date</code>.
	 */
	public Date getCalculationDate() {
		return (Date) getValue(7);
	}

	/**
	 * Setter for <code>farm.sustainability_periods.period_start</code>.
	 */
	public void setPeriodStart(Date value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.period_start</code>.
	 */
	public Date getPeriodStart() {
		return (Date) getValue(8);
	}

	/**
	 * Setter for <code>farm.sustainability_periods.period_end</code>.
	 */
	public void setPeriodEnd(Date value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.sustainability_periods.period_end</code>.
	 */
	public Date getPeriodEnd() {
		return (Date) getValue(9);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Long> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record10 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row10<Timestamp, Timestamp, Long, Long, String, String, Long, Date, Date, Date> fieldsRow() {
		return (Row10) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row10<Timestamp, Timestamp, Long, Long, String, String, Long, Date, Date, Date> valuesRow() {
		return (Row10) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field1() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field7() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Date> field8() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.CALCULATION_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Date> field9() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.PERIOD_START;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Date> field10() {
		return SustainabilityPeriods.SUSTAINABILITY_PERIODS.PERIOD_END;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value1() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value7() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Date value8() {
		return getCalculationDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Date value9() {
		return getPeriodStart();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Date value10() {
		return getPeriodEnd();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value1(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value3(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value5(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value6(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value7(Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value8(Date value) {
		setCalculationDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value9(Date value) {
		setPeriodStart(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord value10(Date value) {
		setPeriodEnd(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SustainabilityPeriodsRecord values(Timestamp value1, Timestamp value2, Long value3, Long value4, String value5, String value6, Long value7, Date value8, Date value9, Date value10) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached SustainabilityPeriodsRecord
	 */
	public SustainabilityPeriodsRecord() {
		super(SustainabilityPeriods.SUSTAINABILITY_PERIODS);
	}

	/**
	 * Create a detached, initialised SustainabilityPeriodsRecord
	 */
	public SustainabilityPeriodsRecord(Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, Long id, Date calculationDate, Date periodStart, Date periodEnd) {
		super(SustainabilityPeriods.SUSTAINABILITY_PERIODS);

		setValue(0, createDate);
		setValue(1, updateDate);
		setValue(2, createAccountId);
		setValue(3, updateAccountId);
		setValue(4, createUi);
		setValue(5, updateUi);
		setValue(6, id);
		setValue(7, calculationDate);
		setValue(8, periodStart);
		setValue(9, periodEnd);
	}
}
