/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.EmailScheduleDeleted;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EmailScheduleDeletedRecord extends UpdatableRecordImpl<EmailScheduleDeletedRecord> implements Record4<Long, Timestamp, Long, String> {

	private static final long serialVersionUID = 1775302654;

	/**
	 * Setter for <code>farm.email_schedule_deleted.id</code>.
	 */
	public void setId(Long value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.email_schedule_deleted.id</code>.
	 */
	public Long getId() {
		return (Long) getValue(0);
	}

	/**
	 * Setter for <code>farm.email_schedule_deleted.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.email_schedule_deleted.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.email_schedule_deleted.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.email_schedule_deleted.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.email_schedule_deleted.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.email_schedule_deleted.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(3);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Long> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record4 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row4<Long, Timestamp, Long, String> fieldsRow() {
		return (Row4) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row4<Long, Timestamp, Long, String> valuesRow() {
		return (Row4) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field1() {
		return EmailScheduleDeleted.EMAIL_SCHEDULE_DELETED.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return EmailScheduleDeleted.EMAIL_SCHEDULE_DELETED.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return EmailScheduleDeleted.EMAIL_SCHEDULE_DELETED.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field4() {
		return EmailScheduleDeleted.EMAIL_SCHEDULE_DELETED.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value1() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value4() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EmailScheduleDeletedRecord value1(Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EmailScheduleDeletedRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EmailScheduleDeletedRecord value3(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EmailScheduleDeletedRecord value4(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EmailScheduleDeletedRecord values(Long value1, Timestamp value2, Long value3, String value4) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached EmailScheduleDeletedRecord
	 */
	public EmailScheduleDeletedRecord() {
		super(EmailScheduleDeleted.EMAIL_SCHEDULE_DELETED);
	}

	/**
	 * Create a detached, initialised EmailScheduleDeletedRecord
	 */
	public EmailScheduleDeletedRecord(Long id, Timestamp updateDate, Long updateAccountId, String updateUi) {
		super(EmailScheduleDeleted.EMAIL_SCHEDULE_DELETED);

		setValue(0, id);
		setValue(1, updateDate);
		setValue(2, updateAccountId);
		setValue(3, updateUi);
	}
}
