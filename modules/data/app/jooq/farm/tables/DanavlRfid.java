/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Date;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.DanavlRfidRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DanavlRfid extends TableImpl<DanavlRfidRecord> {

	private static final long serialVersionUID = 1829020048;

	/**
	 * The reference instance of <code>farm.danavl_rfid</code>
	 */
	public static final DanavlRfid DANAVL_RFID = new DanavlRfid();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<DanavlRfidRecord> getRecordType() {
		return DanavlRfidRecord.class;
	}

	/**
	 * The column <code>farm.danavl_rfid.id</code>.
	 */
	public final TableField<DanavlRfidRecord, String> ID = createField("id", org.jooq.impl.SQLDataType.VARCHAR.length(30).nullable(false), this, "");

	/**
	 * The column <code>farm.danavl_rfid.herd</code>.
	 */
	public final TableField<DanavlRfidRecord, Integer> HERD = createField("herd", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "");

	/**
	 * The column <code>farm.danavl_rfid.smallnumber</code>.
	 */
	public final TableField<DanavlRfidRecord, Integer> SMALLNUMBER = createField("smallnumber", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "");

	/**
	 * The column <code>farm.danavl_rfid.year</code>.
	 */
	public final TableField<DanavlRfidRecord, Integer> YEAR = createField("year", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>farm.danavl_rfid.breed</code>.
	 */
	public final TableField<DanavlRfidRecord, String> BREED = createField("breed", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_rfid.upload_date</code>.
	 */
	public final TableField<DanavlRfidRecord, Date> UPLOAD_DATE = createField("upload_date", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.danavl_rfid.link_date</code>.
	 */
	public final TableField<DanavlRfidRecord, Date> LINK_DATE = createField("link_date", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.danavl_rfid.company</code>.
	 */
	public final TableField<DanavlRfidRecord, Long> COMPANY = createField("company", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_rfid.item</code>.
	 */
	public final TableField<DanavlRfidRecord, Long> ITEM = createField("item", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_rfid.serial_no</code>.
	 */
	public final TableField<DanavlRfidRecord, Long> SERIAL_NO = createField("serial_no", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * Create a <code>farm.danavl_rfid</code> table reference
	 */
	public DanavlRfid() {
		this("danavl_rfid", null);
	}

	/**
	 * Create an aliased <code>farm.danavl_rfid</code> table reference
	 */
	public DanavlRfid(String alias) {
		this(alias, DANAVL_RFID);
	}

	private DanavlRfid(String alias, Table<DanavlRfidRecord> aliased) {
		this(alias, aliased, null);
	}

	private DanavlRfid(String alias, Table<DanavlRfidRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<DanavlRfidRecord> getPrimaryKey() {
		return Keys.DANAVL_RFID_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<DanavlRfidRecord>> getKeys() {
		return Arrays.<UniqueKey<DanavlRfidRecord>>asList(Keys.DANAVL_RFID_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DanavlRfid as(String alias) {
		return new DanavlRfid(alias, this);
	}

	/**
	 * Rename this table
	 */
	public DanavlRfid rename(String name) {
		return new DanavlRfid(name, null);
	}
}
