/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.TransferinSyncRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TransferinSync extends TableImpl<TransferinSyncRecord> {

	private static final long serialVersionUID = 1794813059;

	/**
	 * The reference instance of <code>farm.transferin_sync</code>
	 */
	public static final TransferinSync TRANSFERIN_SYNC = new TransferinSync();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<TransferinSyncRecord> getRecordType() {
		return TransferinSyncRecord.class;
	}

	/**
	 * The column <code>farm.transferin_sync.id</code>.
	 */
	public final TableField<TransferinSyncRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>farm.transferin_sync.traceability_id</code>.
	 */
	public final TableField<TransferinSyncRecord, String> TRACEABILITY_ID = createField("traceability_id", org.jooq.impl.SQLDataType.CLOB.nullable(false), this, "");

	/**
	 * The column <code>farm.transferin_sync.sync_date</code>.
	 */
	public final TableField<TransferinSyncRecord, Timestamp> SYNC_DATE = createField("sync_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.transferin_sync.sync_status</code>.
	 */
	public final TableField<TransferinSyncRecord, String> SYNC_STATUS = createField("sync_status", org.jooq.impl.SQLDataType.CLOB, this, "");

	/**
	 * The column <code>farm.transferin_sync.parent_id</code>.
	 */
	public final TableField<TransferinSyncRecord, Long> PARENT_ID = createField("parent_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * Create a <code>farm.transferin_sync</code> table reference
	 */
	public TransferinSync() {
		this("transferin_sync", null);
	}

	/**
	 * Create an aliased <code>farm.transferin_sync</code> table reference
	 */
	public TransferinSync(String alias) {
		this(alias, TRANSFERIN_SYNC);
	}

	private TransferinSync(String alias, Table<TransferinSyncRecord> aliased) {
		this(alias, aliased, null);
	}

	private TransferinSync(String alias, Table<TransferinSyncRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<TransferinSyncRecord> getPrimaryKey() {
		return Keys.TRANSFERIN_SYNC_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<TransferinSyncRecord>> getKeys() {
		return Arrays.<UniqueKey<TransferinSyncRecord>>asList(Keys.TRANSFERIN_SYNC_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TransferinSync as(String alias) {
		return new TransferinSync(alias, this);
	}

	/**
	 * Rename this table
	 */
	public TransferinSync rename(String name) {
		return new TransferinSync(name, null);
	}
}
