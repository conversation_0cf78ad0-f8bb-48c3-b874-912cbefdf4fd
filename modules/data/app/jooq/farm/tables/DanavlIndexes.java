/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Date;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.DanavlIndexesRecord;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DanavlIndexes extends TableImpl<DanavlIndexesRecord> {

	private static final long serialVersionUID = 937001594;

	/**
	 * The reference instance of <code>farm.danavl_indexes</code>
	 */
	public static final DanavlIndexes DANAVL_INDEXES = new DanavlIndexes();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<DanavlIndexesRecord> getRecordType() {
		return DanavlIndexesRecord.class;
	}

	/**
	 * The column <code>farm.danavl_indexes.id</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_indexes.create_date</code>.
	 */
	public final TableField<DanavlIndexesRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_indexes.animalid</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> ANIMALID = createField("animalid", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.index</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> INDEX = createField("index", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.damid</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> DAMID = createField("damid", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.damnumber</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> DAMNUMBER = createField("damnumber", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.dambreed</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> DAMBREED = createField("dambreed", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.sireid</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> SIREID = createField("sireid", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.sirenumber</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> SIRENUMBER = createField("sirenumber", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.sirebreed</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> SIREBREED = createField("sirebreed", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.male_index</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> MALE_INDEX = createField("male_index", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.female_index</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> FEMALE_INDEX = createField("female_index", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.daily_gain_small</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> DAILY_GAIN_SMALL = createField("daily_gain_small", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.daily_gain_large</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> DAILY_GAIN_LARGE = createField("daily_gain_large", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.feed_units</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> FEED_UNITS = createField("feed_units", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.lean_meat_percentage</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> LEAN_MEAT_PERCENTAGE = createField("lean_meat_percentage", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.litter_size</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> LITTER_SIZE = createField("litter_size", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.sustainability</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> SUSTAINABILITY = createField("sustainability", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.slaughter_loss</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> SLAUGHTER_LOSS = createField("slaughter_loss", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.index_type</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> INDEX_TYPE = createField("index_type", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.comment</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> COMMENT = createField("comment", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "");

	/**
	 * The column <code>farm.danavl_indexes.strength</code>.
	 */
	public final TableField<DanavlIndexesRecord, Long> STRENGTH = createField("strength", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.last_index_update_date</code>.
	 */
	public final TableField<DanavlIndexesRecord, Date> LAST_INDEX_UPDATE_DATE = createField("last_index_update_date", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.danavl_indexes.usecode</code>.
	 */
	public final TableField<DanavlIndexesRecord, Short> USECODE = createField("usecode", org.jooq.impl.SQLDataType.SMALLINT, this, "");

	/**
	 * The column <code>farm.danavl_indexes.entrydate</code>.
	 */
	public final TableField<DanavlIndexesRecord, Date> ENTRYDATE = createField("entrydate", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.danavl_indexes.exittype</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> EXITTYPE = createField("exittype", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.danavl_indexes.exitcode</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> EXITCODE = createField("exitcode", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.danavl_indexes.exitdate</code>.
	 */
	public final TableField<DanavlIndexesRecord, Date> EXITDATE = createField("exitdate", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.danavl_indexes.name</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> NAME = createField("name", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.farmnumber</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> FARMNUMBER = createField("farmnumber", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.birthdate</code>.
	 */
	public final TableField<DanavlIndexesRecord, Date> BIRTHDATE = createField("birthdate", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.danavl_indexes.sex</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> SEX = createField("sex", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_indexes.breed</code>.
	 */
	public final TableField<DanavlIndexesRecord, String> BREED = createField("breed", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * Create a <code>farm.danavl_indexes</code> table reference
	 */
	public DanavlIndexes() {
		this("danavl_indexes", null);
	}

	/**
	 * Create an aliased <code>farm.danavl_indexes</code> table reference
	 */
	public DanavlIndexes(String alias) {
		this(alias, DANAVL_INDEXES);
	}

	private DanavlIndexes(String alias, Table<DanavlIndexesRecord> aliased) {
		this(alias, aliased, null);
	}

	private DanavlIndexes(String alias, Table<DanavlIndexesRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Identity<DanavlIndexesRecord, Long> getIdentity() {
		return Keys.IDENTITY_DANAVL_INDEXES;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<DanavlIndexesRecord> getPrimaryKey() {
		return Keys.DANAVL_INDEXES_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<DanavlIndexesRecord>> getKeys() {
		return Arrays.<UniqueKey<DanavlIndexesRecord>>asList(Keys.DANAVL_INDEXES_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DanavlIndexes as(String alias) {
		return new DanavlIndexes(alias, this);
	}

	/**
	 * Rename this table
	 */
	public DanavlIndexes rename(String name) {
		return new DanavlIndexes(name, null);
	}
}
