/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.DanavlTestingLogsRecord;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DanavlTestingLogs extends TableImpl<DanavlTestingLogsRecord> {

	private static final long serialVersionUID = 2084223714;

	/**
	 * The reference instance of <code>farm.danavl_testing_logs</code>
	 */
	public static final DanavlTestingLogs DANAVL_TESTING_LOGS = new DanavlTestingLogs();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<DanavlTestingLogsRecord> getRecordType() {
		return DanavlTestingLogsRecord.class;
	}

	/**
	 * The column <code>farm.danavl_testing_logs.create_date</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.update_date</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.create_account_id</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Long> CREATE_ACCOUNT_ID = createField("create_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.update_account_id</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.create_ui</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> CREATE_UI = createField("create_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.update_ui</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.id</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.formtype</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> FORMTYPE = createField("formtype", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.version</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> VERSION = createField("version", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.eventid</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Long> EVENTID = createField("eventid", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.location</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> LOCATION = createField("location", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.stable</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> STABLE = createField("stable", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.pen</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> PEN = createField("pen", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.year</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Short> YEAR = createField("year", org.jooq.impl.SQLDataType.SMALLINT, this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.repeat</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Short> REPEAT = createField("repeat", org.jooq.impl.SQLDataType.SMALLINT, this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.animalid</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> ANIMALID = createField("animalid", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.breed</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> BREED = createField("breed", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.sex</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> SEX = createField("sex", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.startdate</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Date> STARTDATE = createField("startdate", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.first_startdate</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Date> FIRST_STARTDATE = createField("first_startdate", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.last_startdate</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Date> LAST_STARTDATE = createField("last_startdate", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.animalscount</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Integer> ANIMALSCOUNT = createField("animalscount", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.startweight</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, BigDecimal> STARTWEIGHT = createField("startweight", org.jooq.impl.SQLDataType.NUMERIC.precision(8, 5), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.status</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Short> STATUS = createField("status", org.jooq.impl.SQLDataType.SMALLINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.error_params</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> ERROR_PARAMS = createField("error_params", org.jooq.impl.SQLDataType.VARCHAR.length(255), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.error_comment</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> ERROR_COMMENT = createField("error_comment", org.jooq.impl.SQLDataType.VARCHAR.length(255), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.errorcode</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Integer> ERRORCODE = createField("errorcode", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.errortype</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Integer> ERRORTYPE = createField("errortype", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.comment</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, String> COMMENT = createField("comment", org.jooq.impl.SQLDataType.VARCHAR.length(255), this, "");

	/**
	 * The column <code>farm.danavl_testing_logs.danavlmessage_id</code>.
	 */
	public final TableField<DanavlTestingLogsRecord, Long> DANAVLMESSAGE_ID = createField("danavlmessage_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * Create a <code>farm.danavl_testing_logs</code> table reference
	 */
	public DanavlTestingLogs() {
		this("danavl_testing_logs", null);
	}

	/**
	 * Create an aliased <code>farm.danavl_testing_logs</code> table reference
	 */
	public DanavlTestingLogs(String alias) {
		this(alias, DANAVL_TESTING_LOGS);
	}

	private DanavlTestingLogs(String alias, Table<DanavlTestingLogsRecord> aliased) {
		this(alias, aliased, null);
	}

	private DanavlTestingLogs(String alias, Table<DanavlTestingLogsRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Identity<DanavlTestingLogsRecord, Long> getIdentity() {
		return Keys.IDENTITY_DANAVL_TESTING_LOGS;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<DanavlTestingLogsRecord> getPrimaryKey() {
		return Keys.DANAVL_TESTING_LOGS_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<DanavlTestingLogsRecord>> getKeys() {
		return Arrays.<UniqueKey<DanavlTestingLogsRecord>>asList(Keys.DANAVL_TESTING_LOGS_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<ForeignKey<DanavlTestingLogsRecord, ?>> getReferences() {
		return Arrays.<ForeignKey<DanavlTestingLogsRecord, ?>>asList(Keys.DANAVL_TESTING_LOGS__DANAVL_TESTING_LOGS_DANAVLMESSAGE_ID_FKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<DanavlTestingLogsRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DanavlTestingLogs as(String alias) {
		return new DanavlTestingLogs(alias, this);
	}

	/**
	 * Rename this table
	 */
	public DanavlTestingLogs rename(String name) {
		return new DanavlTestingLogs(name, null);
	}
}
