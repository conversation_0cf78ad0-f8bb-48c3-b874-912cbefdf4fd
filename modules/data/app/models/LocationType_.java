package models;

import javax.annotation.Generated;
import jakarta.persistence.metamodel.SingularAttribute;
import jakarta.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(LocationType.class)
public abstract class LocationType_ {

	public static volatile SingularAttribute<LocationType, Integer> level;
	public static volatile SingularAttribute<LocationType, String> name;
	public static volatile SingularAttribute<LocationType, Integer> minChildLevel;
	public static volatile SingularAttribute<LocationType, String> id;
	public static volatile SingularAttribute<LocationType, Integer> maxChildLevel;

}

