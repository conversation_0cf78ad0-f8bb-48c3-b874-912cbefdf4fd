package models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import jakarta.persistence.MappedSuperclass;

@MappedSuperclass
@JsonIgnoreProperties({"createAccount", "createDate", "createUi", "updateAccount", "updateUi"})
public abstract class TranslationOf<T extends TranslatedCodeTable<T>> extends BaseWithCode {
    public abstract T getParent();
    public abstract void setParent(T parent);
}
