package models;

import javax.annotation.Generated;
import jakarta.persistence.metamodel.SingularAttribute;
import jakarta.persistence.metamodel.StaticMetamodel;
import security.CloudfarmsModule;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(SystemModuleGrant.class)
public abstract class SystemModuleGrant_ extends models.ModuleGrant_ {

	public static volatile SingularAttribute<SystemModuleGrant, CloudfarmsModule> module;

}

