package models;


import com.cloudfarms.pigs.sync.entities.BatchLocationDTO;
import jakarta.persistence.*;

import java.util.Date;


/**
 * Created by Milan Satala
 * Date: 1/7/17
 * Time: 9:06 AM
 */
@Entity(name = "BatchLocation")
@Table(name = "batch_location")
@NamedQueries({
    @NamedQuery(name = "BatchLocation.for.batch", query = "select a from BatchLocation a where a.batch = :batch")
})
public class BatchLocation extends BaseWithId {
    private Location location;
    private Location batch;
    private Date entryDate;

    @ManyToOne
    @JoinColumn(name = "location_id")
    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    @ManyToOne
    @JoinColumn(name = "batch_id")
    public Location getBatch() {
        return batch;
    }

    public void setBatch(Location batch) {
        this.batch = batch;
    }

    @Column(name = "entry_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(Date entryDate) {
        this.entryDate = entryDate;
    }

    @Override
    public BatchLocationDTO toDTO() {
        BatchLocationDTO dto = new BatchLocationDTO();
        dto.setId(getId());
        dto.setLocationId(location.getId());
        dto.setBatchLocationId(batch.getId());
        dto.setEntryDate(entryDate);
        dto.setActorDate(getCreateDate());
        dto.setActorId(getCreateAccountId());
        return dto;
    }

}
