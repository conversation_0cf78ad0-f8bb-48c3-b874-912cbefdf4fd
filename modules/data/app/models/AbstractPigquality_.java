package models;

import java.math.BigDecimal;
import java.sql.Timestamp;
import javax.annotation.Generated;
import jakarta.persistence.metamodel.SingularAttribute;
import jakarta.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(AbstractPigquality.class)
public abstract class AbstractPigquality_ extends models.BaseWithId_ {

	public static volatile SingularAttribute<AbstractPigquality, Timestamp> updateDate;
	public static volatile SingularAttribute<AbstractPigquality, BigDecimal> pigWeight;
	public static volatile SingularAttribute<AbstractPigquality, String> pigQualityTypeCode;
	public static volatile SingularAttribute<AbstractPigquality, BigDecimal> pigLiveWeight;
	public static volatile SingularAttribute<AbstractPigquality, BigDecimal> leanPercent;
	public static volatile SingularAttribute<AbstractPigquality, String> comment;
	public static volatile SingularAttribute<AbstractPigquality, Integer> pigAmount;
	public static volatile SingularAttribute<AbstractPigquality, BigDecimal> deadWeight;
	public static volatile SingularAttribute<AbstractPigquality, Integer> deadAmount;
}

