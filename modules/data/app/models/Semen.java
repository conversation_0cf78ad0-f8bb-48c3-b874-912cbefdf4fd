package models;

import com.cloudfarms.pigs.sync.entities.SemenDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import generators.Display;
import org.eclipse.persistence.annotations.JoinFetch;
import org.eclipse.persistence.annotations.JoinFetchType;
import org.eclipse.persistence.config.HintValues;
import org.eclipse.persistence.config.QueryHints;

import jakarta.persistence.*;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Table(name = "semen")
@Entity
//@NamedNativeQueries({
//    @NamedNativeQuery(name = "semen.lastTappings", query = "select distinct on (boar_id) s.semenDate, s.boar_id, b.boarNumber from semen s right join boar b on " +
//                             "s.boar_id = b.id and b.active order by boar_id, semendate desc")})
    //@NamedNativeQuery(name = "semen.lastTappings", query = "select distinct on (boar_id) *from semen order by boar_id, semendate desc")})         // original Gregors query

@NamedQueries({
    @NamedQuery(name ="semen.desc.byBoar", query ="select s from Semen s where s.boar = :boar order by s.actorDate desc"),
    @NamedQuery(name ="semen.asc.byBoar", query ="select s from Semen s where s.boar = :boar order by s.actorDate asc"),
    @NamedQuery(name="semen.updated.after",query="select s from Semen s where s.updateDate > :after", hints={@QueryHint(name=QueryHints.LEFT_FETCH, value="s.semenBatch")})
})

@NamedNativeQueries({
    @NamedNativeQuery( name="semen.deleted.after", query="select id from semen_deleted where update_date > ?", hints = {@QueryHint(name = QueryHints.CURSOR, value = HintValues.TRUE) } )
})

@JsonIgnoreProperties(ignoreUnknown=true)
public class Semen extends BaseWithId {

    private Long boarId;
    private Boar boar;
    private ExternalBoar externalBoar;
    private Colorimeter colorimeter;
    private Long colorimeterId;
    private SemenBatch semenBatch;
    private Integer amountRawSemen;
    private Integer usedDiluentAmount;
    private boolean colorRight;
    private boolean smellRight;
    private boolean spermMoving;
    private BigDecimal semenmeterpluscalc;
    private Integer motilityPercent;
    private Integer motilityPercent1;
    private Integer motilityPercent2;
    private Integer motilityPercent3;
    private BigDecimal colorimeterReading;
    private Integer portionNumber;
    private Integer diluentAmount;
 //   private Date semenDate;
    private Integer tubeSize;
    private String comment;
    private Long actorId;
    private Date actorDate;
    private String animalId;
    private Timestamp updateDate;
    private String breed;
    private int usedSemenBatches;

    @Max(9999)
    @Min(0)
    public Integer getUsedDiluentAmount() { return usedDiluentAmount; }
    public void setUsedDiluentAmount(Integer usedDiluentAmount) { this.usedDiluentAmount = usedDiluentAmount; }

    @Column(length = 4, precision = 2)
    public BigDecimal getSemenmeterpluscalc() { return semenmeterpluscalc; }
    public void setSemenmeterpluscalc(BigDecimal semenmeterpluscalc) { this.semenmeterpluscalc = semenmeterpluscalc; }

    @Transient
    public ExternalBoar getExternalBoar() {
        return externalBoar;
    }
    public void setExternalBoar(ExternalBoar externalBoar) {
        this.externalBoar = externalBoar;
    }

    @Override
    public void setId(long id) {
        super.setId(id);
    }

    @Size(max = 30)
    public String getBreed() {
        return breed;
    }
    public void setBreed(String breed) {
        this.breed = breed;
    }

    public String getAnimalId() {
        return animalId != null ? animalId : (boar != null ? boar.getAnimalId() : null);
    }
    public void setAnimalId(String animalId) {
        this.animalId = animalId;
    }

    @Column(name = "boar_id", insertable = false, updatable = false)
    @JsonIgnore
    public Long getBoarId() {
        return boarId;
    }
    public void setBoarId(Long boarId) {
        this.boarId = boarId;
    }

    @JoinFetch(JoinFetchType.OUTER)
    @ManyToOne(fetch = FetchType.LAZY, optional = false, cascade=CascadeType.MERGE)
    public Boar getBoar() {
        return boar;
    }
    public void setBoar(Boar boar) {
        this.boar = boar;
    }
    
    @Column(name="ACTOR_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getActorDate() {
        return actorDate;
    }

    public void setActorDate(Date actorDate) {
        this.actorDate = actorDate;
    }   
    
    @Column(name="COLORIMETER_ID")
    public Long getColorimeterId() {
        return colorimeterId;
    }
    public void setColorimeterId(Long colorimeterId) {
        this.colorimeterId = colorimeterId;
    }
    
    @Column(name="ACTOR_ID")
    public Long getActorId() {
        return actorId;
    }
    public void setActorId(Long actorId) {
        this.actorId = actorId;
    }

    //  @JoinFetch(JoinFetchType.OUTER)
    @JsonIgnore
    @JoinColumn(insertable=false, updatable=false)
    @ManyToOne(fetch = FetchType.LAZY)
    public Colorimeter getColorimeter() {
        return colorimeter;
    }

    public void setColorimeter(Colorimeter colorimeter) {
        this.colorimeter = colorimeter;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = true/*, cascade=CascadeType.ALL*/)
    public SemenBatch getSemenBatch() {
        return semenBatch;
    }

    public void setSemenBatch(SemenBatch semenBatch) {
        this.semenBatch = semenBatch;
    }

    @Transient
    public int getUsedSemenBatches(){
        return usedSemenBatches;
    }

    public void setUsedSemenBatches(int usedSemenBatches) {
        this.usedSemenBatches = usedSemenBatches;
    }

    @Max(9999)
    @Min(0)
    public Integer getAmountRawSemen() {
        return amountRawSemen;
    }

    public void setAmountRawSemen(Integer amountRawSemen) {
        this.amountRawSemen = amountRawSemen;
    }
    
    @Size(max=300)
    public String getComment() {
        return comment;
    }
    public void setComment(String comment) {
        this.comment = comment;
    }
    
    public Integer getTubeSize() {
        return tubeSize;
    }

    public void setTubeSize(Integer tubeSize) {
        this.tubeSize = tubeSize;
    }

    public boolean isColorRight() {
        return colorRight;
    }

    public void setColorRight(boolean colorRight) {
        this.colorRight = colorRight;
    }

    public boolean isSmellRight() {
        return smellRight;
    }

    public void setSmellRight(boolean smellRight) {
        this.smellRight = smellRight;
    }

    public boolean isSpermMoving() {
        return spermMoving;
    }

    public void setSpermMoving(boolean spermMoving) {
        this.spermMoving = spermMoving;
    }

    @Max(100)
    @Min(0)
    public Integer getMotilityPercent1() {
        return motilityPercent1;
    }

    public void setMotilityPercent1(Integer motilityPercent) {
        this.motilityPercent1 = motilityPercent;
    }

    @Max(100)
    @Min(0)
    public Integer getMotilityPercent2() {
        return motilityPercent2;
    }

    public void setMotilityPercent2(Integer motilityPercent) {
        this.motilityPercent2 = motilityPercent;
    }

    @Max(100)
    @Min(0)
    public Integer getMotilityPercent3() {
        return motilityPercent3;
    }

    public void setMotilityPercent3(Integer motilityPercent) {
        this.motilityPercent3 = motilityPercent;
    }

    @Max(100)
    @Min(0)
    public Integer getMotilityPercent() {
        return motilityPercent;
    }

    public void setMotilityPercent(Integer motilityPercent) {
        this.motilityPercent = motilityPercent;
    }

    @Column(length = 4, precision = 2)
    public BigDecimal getColorimeterReading() {
        return colorimeterReading;
    }

    public void setColorimeterReading(BigDecimal colorimeterReading) {
        this.colorimeterReading = colorimeterReading;
    }

    @Max(999)
    @Min(0)
    public Integer getPortionNumber() {
        return portionNumber;
    }

    public void setPortionNumber(Integer portionNumber) {
        this.portionNumber = portionNumber;
    }

    @Max(9999)
    @Min(0)
    public Integer getDiluentAmount() {
        return diluentAmount;
    }

    public void setDiluentAmount(Integer diluentAmount) {
        this.diluentAmount = diluentAmount;
    }

//    @Temporal(TemporalType.TIMESTAMP)
//    public Date getSemenDate() {
//        return semenDate;
//    }
//
//    public void setSemenDate(Date semenDate) {
//        this.semenDate = semenDate;
//    }


    @Column(name = "update_date")
    @Version
    @Display(filters = "timestamp")
    public Timestamp getUpdateDate() {
    	return updateDate;
    }

    protected void setUpdateDate(Timestamp updateDate) {
    	this.updateDate = updateDate;
    }

    public SemenDTO toDTO(){
        SemenDTO dto = new SemenDTO();
        dto.setId(getId());
        dto.setBoarId(getBoar()!=null ? getBoar().getId() : null);
        dto.setColorimeterId(getColorimeterId());
        dto.setSemenBatchId(getSemenBatch()!=null ? getSemenBatch().getId() : null);
        dto.setAmountRawSemen(getAmountRawSemen());
        dto.setColorRight(isColorRight());
        dto.setSmellRight(isSmellRight());
        dto.setSpermMoving(isSpermMoving());
        dto.setMotilityPercent(getMotilityPercent());
        dto.setMotilityPercent1(getMotilityPercent1());
        dto.setMotilityPercent2(getMotilityPercent2());
        dto.setMotilityPercent3(getMotilityPercent3());
        dto.setColorimeterReading(doubleValue(getColorimeterReading()));
        dto.setPortionNumber(getPortionNumber());
        dto.setDiluentAmount(getDiluentAmount());
        dto.setTubeSize(getTubeSize());
        dto.setComment(getComment());
        dto.setActorId(getActorId());
        dto.setActorDate(getActorDate());
        dto.setAnimalId(getAnimalId());
        dto.setBreed(getBreed());
        return dto;
    }
}
