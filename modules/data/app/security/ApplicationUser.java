package security;

import be.objectify.deadbolt.java.models.Permission;
import be.objectify.deadbolt.java.models.Subject;
import com.cloudfarms.settings.security.JavaGrant;
import contract.Organization;
import com.cloudfarms.settings.Setting;
import com.cloudfarms.settings.Settings;
import security.scala.AppPermission;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toSet;


public class ApplicationUser implements Organization.IRootFarmsProvider, UserAndFarm {
    public static final Set<String> CF_EMAIL_ENDINGS = Stream.of("@cloudfarms.com", "@dva.tri").collect(toSet());

    public final Setting.PersonaInfo personaInfo;

    public final ApplicationUserSubject asSubject;

    private final List<AppPermission> permissions;

    public class ApplicationUserSubject implements Subject {
        private final List<? extends be.objectify.deadbolt.java.models.Role> deadboltRoles = personaInfo.javaRoles().stream().map(r -> (be.objectify.deadbolt.java.models.Role) r::getName).collect(Collectors.toList());

        @Override
        public List<? extends be.objectify.deadbolt.java.models.Role> getRoles() {
            return deadboltRoles;
        }

        @Override
        public List<? extends Permission> getPermissions() {
            return permissions;
        }

        @Override
        public String getIdentifier() {
            return getUserName() + " " + personaInfo.persona().farm().name();
        }

        public ApplicationUser getApplicationUser() {
            return ApplicationUser.this;
        }
    }

    public ApplicationUser(Setting.PersonaInfo personaInfo) {
        this.personaInfo = personaInfo;
        this.asSubject = new ApplicationUserSubject();

        final List<String> ps = personaInfo.acc().javaPermissions();
        this.permissions = ps.isEmpty()
            ? Collections.emptyList()
            : ps.stream().map(AppPermission::new).collect(Collectors.toList());
    }

    public List<Role> getRoles() {
        return personaInfo.javaRoles();
    }

    public Setting.Farm getFarm() {
        return personaInfo.persona().farm();
    }

    public String getDatabaseName() {
        return getFarm().databaseName();
    }

    public String getReportsDatabaseName() {
        return getFarm().reportsDatabaseName();
    }

    public String getUserName() {
        return personaInfo.userName();
    }

    public String getFarmName() {
        return personaInfo.persona().farm().name();
    }

    public Settings farmSettings() {
        return personaInfo.farmSettings();
    }

    public Settings settings() {
        return personaInfo.settings();
    }

    public boolean hasFarm(long farmId) {
        return personaInfo.acc().javaFarms().stream().anyMatch(o -> o.id() == farmId);
    }

    public UserAndFarm getUserAndFarm() {
        return new UserAndFarmIds(farmId(), userId());
    }

    public long farmId() {
        return personaInfo.persona().farm().id();
    }

    public long userId() {
        return personaInfo.acc().id();
    }

    public String getHash2() {
        return personaInfo.acc().hash2();
    }

    public boolean isPaying() {
        final LocalDate graceLimit = LocalDate.now();

        return getModules().stream().anyMatch(grant -> grant.getModule() == CloudfarmsModule.core &&
            (grant.getAccessType() == 'h' || grant.getAccessType() == 'o') &&
            (grant.getExpires() == null || !grant.getExpires().isBefore(graceLimit))
        );
    }

    public boolean isAdminOrDeveloper() {
        return personaInfo.javaRoles().contains(AppRole.admin) || personaInfo.javaRoles().contains(AppRole.developer);
    }

    public boolean isAppAdmin() {
        return personaInfo.javaRoles().contains(AppRole.admin);
    }

    public boolean isAppSecurityOfficer() {
        return personaInfo.javaRoles().contains(AppRole.securityOfficer);
    }

    public boolean isAccounting() {
        return personaInfo.javaRoles().contains(AppRole.accounting);
    }

    public boolean isBackOffice() {
        return personaInfo.javaRoles().contains(AppRole.backOffice);
    }

    public long getPersonaId() {
        return personaInfo.persona().id();
    }

    public long getAccountId() {
        return personaInfo.acc().id();
    }

    public long getFarmId() {
        return personaInfo.persona().farm().id();
    }

    public Long getRootId() {
        return personaInfo.persona().farm().rootId();
    }

    public String getHoldingName(long holdingId) {
        for (Setting.Holding holding : personaInfo.persona().farm().javaParents()) {
            if (holding.id() == holdingId) return holding.name();
        }
        return "-";
    }

    public List<JavaGrant> getModules() {
        return personaInfo.javaGrants();
    }

    public boolean hasModule(CloudfarmsModule module) {
        return getModules().stream().anyMatch(g -> g.getModule() == module);
    }

    public boolean hasActiveModule(CloudfarmsModule module) {
        return getModules().stream().anyMatch(g -> g.getModule() == module && g.isActive());
    }

    public Map<? extends Long, ? extends String> getHoldingNames() {
        Map<Long, String> holdingNames = new HashMap<>();
        for (Setting.Holding h : personaInfo.persona().farm().javaParents()) {
            holdingNames.put(h.id(), h.name());
        }

        return holdingNames;
    }

    public String getFarmCountryCode() {
        if (personaInfo.persona().farm().countryCode().isEmpty()) return null;
        return personaInfo.persona().farm().countryCode().get();
    }

    public String getName() {
        return personaInfo.acc().name();
    }

    public String getUserFunction() {
        return personaInfo.persona().userFunction().getOrElse(Setting.defaultForJava(""));
    }

    public String getFarmTimezoneName() {
        return personaInfo.persona().farm().timeZoneName().get();
    }

    public List<Setting.Farm> getFarms() {
        return personaInfo.javaFarms();
    }

    public scala.collection.immutable.List<Setting.Farm> getFarmsScala() {
        return personaInfo.farms();
    }

    @Override
    public scala.collection.immutable.List<Organization.IFarm> getRootFarms() {
        return (scala.collection.immutable.List<Organization.IFarm>) (scala.collection.immutable.List) this.getRootFarmsHelperScala();
    }

    public List<Setting.Holding> getParents() {
        return personaInfo.persona().farm().javaParents();
    }

    /**
     * @return true if the user has a CF email address
     */
    public boolean isCfUser() {
        return CF_EMAIL_ENDINGS.stream().anyMatch(getUserName()::endsWith);
    }

    public boolean hasOneOfRoles(List<Role> roles) {
        return roles.stream().map(Role::getName).anyMatch(role -> getRoles().stream().map(Role::getName).toList().contains(role));
    }

    private List<Setting.Farm> getRootFarmsHelper() {
        return this.getFarms().stream().filter(farm -> farm.rootId() == getRootId()).collect(Collectors.toList());
    }

    private scala.collection.immutable.List<Setting.Farm> getRootFarmsHelperScala() {
        return scala.jdk.javaapi.CollectionConverters.asScala(this.getRootFarmsHelper()).toList();
    }
}
