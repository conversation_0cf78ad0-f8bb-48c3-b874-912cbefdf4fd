package security.scala

import be.objectify.deadbolt.scala.models.{ Permission, Role, Subject }
import security.ApplicationUser

import scala.jdk.CollectionConverters._
import scala.collection.immutable

/**
  * Created by Milan Satala
  * Date: 26.5.2016
  * Time: 14:17
  */
case class ScalaSubject(user: ApplicationUser) extends Subject {
  val identifier: String = user.asSubject.getIdentifier

  lazy val permissions: List[Permission] =
    user.asSubject.getPermissions.asScala.map(javaPermission => AppPermission(javaPermission.getValue)).toList

  lazy val roles: immutable.List[Role] = user.getRoles.asScala.map(javaRole => {
    new Role {
      def name = javaRole.getName
    }
  }).toList
}
