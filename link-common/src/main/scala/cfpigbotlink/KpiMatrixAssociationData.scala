package cfpigbotlink

import com.github.plokhotnyuk.jsoniter_scala.core._
import com.github.plokhotnyuk.jsoniter_scala.macros._

/**
 * @param leftCode KPI code of the left side association
 * @param rightCode KPI code of the right side association
 * @param quality number 0 to 3, 0 - no association, 1 - weak association, 3 - strong association
 * @param explanation explanation why the KPI is associated with the given KPI
 */
case class KpiMatrixAssociationData(leftCode: String, rightCode: String, quality: Int, explanation: String)
