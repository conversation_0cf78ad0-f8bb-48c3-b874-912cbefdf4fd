package qroot.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class MedicintypeLangX(
  createDate: DynamicValue[Option[java.time.Instant]] = DynamicValue.Unavailable,
  updateDate: DynamicValue[Option[java.time.Instant]] = DynamicValue.Unavailable,
  createAccountId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  code: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  name: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  description: DynamicValue[Option[String]] = DynamicValue.Unavailable,
)

object MedicintypeLangX {
  import quill.QuillContext._

  implicit def setMedicintypeLangX(v: MedicintypeLangX): Seq[DynamicSet[qroot.MedicintypeLangX, _]] = {
    val set = ValueSetter[qroot.MedicintypeLangX]
    Seq(
      set(_.createDate)(v.createDate),
      set(_.updateDate)(v.updateDate),
      set(_.createAccountId)(v.createAccountId),
      set(_.updateAccountId)(v.updateAccountId),
      set(_.code)(v.code),
      set(_.name)(v.name),
      set(_.description)(v.description),
    )
  }
}
