package qpublic.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class News(
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  accountId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  createDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  content: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  topic: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  validFrom: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  countries: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  language: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  farmTypeId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  hashtags: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  newsType: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  createAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
)

object News {
  import quill.QuillContext._

  implicit def setNews(v: News): Seq[DynamicSet[qpublic.News, _]] = {
    val set = ValueSetter[qpublic.News]
    Seq(
      set(_.id)(v.id),
      set(_.accountId)(v.accountId),
      set(_.updateAccountId)(v.updateAccountId),
      set(_.createDate)(v.createDate),
      set(_.updateDate)(v.updateDate),
      set(_.content)(v.content),
      set(_.topic)(v.topic),
      set(_.validFrom)(v.validFrom),
      set(_.countries)(v.countries),
      set(_.language)(v.language),
      set(_.farmTypeId)(v.farmTypeId),
      set(_.hashtags)(v.hashtags),
      set(_.newsType)(v.newsType),
      set(_.createAccountId)(v.createAccountId),
    )
  }
}
