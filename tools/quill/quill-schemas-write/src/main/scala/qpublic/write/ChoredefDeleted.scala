package qpublic.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class ChoredefDeleted(
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
)

object ChoredefDeleted {
  import quill.QuillContext._

  implicit def setChoredefDeleted(v: ChoredefDeleted): Seq[DynamicSet[qpublic.ChoredefDeleted, _]] = {
    val set = ValueSetter[qpublic.ChoredefDeleted]
    Seq(
      set(_.id)(v.id),
      set(_.updateDate)(v.updateDate),
      set(_.updateAccountId)(v.updateAccountId),
    )
  }
}
